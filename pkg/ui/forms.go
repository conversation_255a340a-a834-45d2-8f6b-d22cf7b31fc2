package ui

import (
	"fmt"
	"strconv"
	"strings"

	"bella/pkg/copier"
	"bella/pkg/device"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// applyFormTheme styles a form and its dropdowns for readability
func (a *App) applyFormTheme(form *tview.Form) {
	for i := 0; i < form.GetFormItemCount(); i++ {
		item := form.GetFormItem(i)
		if dropdown, ok := item.(*tview.DropDown); ok {
			dropdown.SetFieldTextColor(a.theme.PrimaryText)
			dropdown.SetFieldBackgroundColor(a.theme.SecondaryBg)
			dropdown.SetListStyles(
				tcell.StyleDefault.Foreground(a.theme.PrimaryText).Background(a.theme.SecondaryBg),
				tcell.StyleDefault.Foreground(a.theme.PrimaryText).Background(a.theme.SelectedColor),
			)
		}
	}
}

// showCopyForm displays the form for copy operations.
func (a *App) showCopyForm() {
	pageName := "copyForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Input", "", 40, nil, nil).
		AddInputField("Output", "", 40, nil, nil).
		AddInputField("Block Size", "auto", 10, nil, nil).
		AddInputField("Count", "-1", 10, nil, nil).
		AddInputField("Skip", "0", 10, nil, nil).
		AddInputField("Seek", "0", 10, nil, nil).
		AddDropDown("Compression", []string{"none", "compress", "decompress", "auto"}, 0, nil)

	compressionTypeDropDown := tview.NewDropDown().
		SetLabel("Compression Type").
		SetOptions([]string{"tar.gz", "tar.xz", "zip"}, nil)

	compressionLevelDropDown := tview.NewDropDown().
		SetLabel("Compression Level").
		SetOptions([]string{"Good", "Better", "Best"}, nil)

	compressionTypeDropDown.SetSelectedFunc(func(text string, index int) {
		if text == "zip" {
			compressionLevelDropDown.SetLabel("Compression Level").
				SetOptions([]string{"N/A"}, nil).
				SetCurrentOption(0)
		} else {
			compressionLevelDropDown.SetLabel("Compression Level").
				SetOptions([]string{"Good", "Better", "Best"}, nil).
				SetCurrentOption(0)
		}
	})

	form.AddFormItem(compressionTypeDropDown).
		AddFormItem(compressionLevelDropDown).
		AddDropDown("Checksum", []string{"none", "sha256", "md5", "sha1"}, 0, nil).
		AddCheckbox("Sparse Copy", false, nil).
		AddCheckbox("Skip Bad Sectors", false, nil).
		AddCheckbox("Verify After Copy", false, nil).
		AddCheckbox("Append Mode", false, nil).
		AddCheckbox("Disable Kernel Copy Offload", false, nil).
		AddCheckbox("Dry Run", false, nil)

	a.applyFormTheme(form)

	form.AddButton("Browse Input", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})
	form.AddButton("Browse Output", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(1).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Copy", func() {
		cfg.Operation = copier.OpCopy
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()

		bsText := form.GetFormItem(2).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Input)
			if err != nil {
				a.showDetailedError("Auto Block Size", err)
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showDetailedError("Invalid Block Size", err)
				return
			}
			cfg.BlockSize = bs
		}

		count, _ := strconv.Atoi(form.GetFormItem(3).(*tview.InputField).GetText())
		cfg.Count = count
		skip, _ := strconv.ParseInt(form.GetFormItem(4).(*tview.InputField).GetText(), 10, 64)
		cfg.Skip = skip
		seek, _ := strconv.ParseInt(form.GetFormItem(5).(*tview.InputField).GetText(), 10, 64)
		cfg.Seek = seek

		_, cfg.Compression = form.GetFormItem(6).(*tview.DropDown).GetCurrentOption()
		_, cfg.CompressionType = compressionTypeDropDown.GetCurrentOption()
		_, compressionLevel := compressionLevelDropDown.GetCurrentOption()

		// Handle ZIP compression level (N/A case)
		if compressionLevel == "N/A" {
			cfg.CompressionLevel = "Good" // Default fallback, though not used for ZIP
		} else {
			cfg.CompressionLevel = compressionLevel
		}

		checksumDropDown := form.GetFormItemByLabel("Checksum").(*tview.DropDown)
		_, checksumAlgorithm := checksumDropDown.GetCurrentOption()
		if checksumAlgorithm == "none" {
			cfg.Checksum = ""
		} else {
			cfg.Checksum = checksumAlgorithm
		}

		cfg.Sparse = form.GetFormItemByLabel("Sparse Copy").(*tview.Checkbox).IsChecked()
		cfg.SkipBadSectors = form.GetFormItemByLabel("Skip Bad Sectors").(*tview.Checkbox).IsChecked()
		cfg.Verify = form.GetFormItemByLabel("Verify After Copy").(*tview.Checkbox).IsChecked()
		cfg.Append = form.GetFormItemByLabel("Append Mode").(*tview.Checkbox).IsChecked()
		cfg.UseCopyOffload = !form.GetFormItemByLabel("Disable Kernel Copy Offload").(*tview.Checkbox).IsChecked()
		cfg.DryRun = form.GetFormItemByLabel("Dry Run").(*tview.Checkbox).IsChecked()

		a.runOperation(cfg)
	})
	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Copy Operation")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showVerifyForm displays the form for verify operations.
func (a *App) showVerifyForm() {
	pageName := "verifyForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Source", "", 40, nil, nil).
		AddInputField("Target", "", 40, nil, nil).
		AddInputField("Block Size", "auto", 10, nil, nil).
		AddDropDown("Checksum", []string{"none", "sha256", "md5", "sha1"}, 0, nil)

	a.applyFormTheme(form)

	form.AddButton("Browse Source", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})
	form.AddButton("Browse Target", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(1).(*tview.InputField).SetText(path)
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Verify", func() {
		cfg.Operation = copier.OpVerify
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()

		bsText := form.GetFormItem(2).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Input)
			if err != nil {
				a.showDetailedError("Auto Block Size", err)
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showDetailedError("Invalid Block Size", err)
				return
			}
			cfg.BlockSize = bs
		}

		_, checksumAlgorithm := form.GetFormItem(3).(*tview.DropDown).GetCurrentOption()
		if checksumAlgorithm == "none" {
			cfg.Checksum = ""
		} else {
			cfg.Checksum = checksumAlgorithm
		}

		a.runOperation(cfg)
	})

	form.AddButton("Back", func() { a.goBack(pageName) })
	form.SetBorder(true).SetTitle(" Verify Files ")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showWipeForm displays the form for wipe operations.
func (a *App) showWipeForm() {
	pageName := "wipeForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Target Device", "", 40, nil, nil).
		AddDropDown("Mode", []string{"zero", "random"}, 1, nil).
		AddInputField("Passes", "1", 3, nil, nil).
		AddInputField("Block Size", "4M", 10, nil, nil)

	a.applyFormTheme(form)

	form.AddButton("Browse Target", func() {
		a.showDeviceSelector(pageName, func(path string) {
			form.GetFormItem(0).(*tview.InputField).SetText(path)
			form.GetFormItem(3).(*tview.InputField).SetText("auto")
			a.switchToPage(pageName)
		})
	})

	form.AddButton("Start Wipe", func() {
		cfg.Operation = copier.OpWipe
		cfg.Output = form.GetFormItem(0).(*tview.InputField).GetText()
		_, cfg.WipeMode = form.GetFormItem(1).(*tview.DropDown).GetCurrentOption()
		passes, _ := strconv.Atoi(form.GetFormItem(2).(*tview.InputField).GetText())
		if passes < 1 {
			passes = 1
		}
		cfg.WipePasses = passes

		bsText := form.GetFormItem(3).(*tview.InputField).GetText()
		if strings.ToLower(bsText) == "auto" {
			suggestedBS, err := device.SuggestBlockSize(cfg.Output)
			if err != nil {
				a.showDetailedError("Auto Block Size", err)
				return
			}
			cfg.BlockSize = suggestedBS
		} else {
			bs, err := parseBlockSize(bsText)
			if err != nil {
				a.showDetailedError("Invalid Block Size", err)
				return
			}
			cfg.BlockSize = bs
		}

		a.showConfirmation(fmt.Sprintf("This will IRREVERSIBLY DESTROY ALL DATA on %s.\nAre you sure?", cfg.Output), func() {
			a.runOperation(cfg)
		})
	})

	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Wipe Operation")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showCompressForm displays a standalone compression form.
func (a *App) showCompressForm() {
	pageName := "compressForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Input", "", 40, nil, nil).
		AddInputField("Output (without extension)", "", 40, nil, nil)

	compressionTypeDropDown := tview.NewDropDown().
		SetLabel("Compression Type").
		SetOptions([]string{"tar.gz", "tar.xz", "zip"}, nil)

	compressionLevelDropDown := tview.NewDropDown().
		SetLabel("Compression Level").
		SetOptions([]string{"Good", "Better", "Best"}, nil)

	compressionTypeDropDown.SetSelectedFunc(func(text string, index int) {
		if text == "zip" {
			// ZIP compression level is handled internally by the zip library
			compressionLevelDropDown.SetLabel("Compression Level").
				SetOptions([]string{"N/A"}, nil).
				SetCurrentOption(0)
		} else {
			// tar.gz and tar.xz support compression levels
			compressionLevelDropDown.SetLabel("Compression Level").
				SetOptions([]string{"Good", "Better", "Best"}, nil).
				SetCurrentOption(0)
		}
	})

	form.AddFormItem(compressionTypeDropDown).
		AddFormItem(compressionLevelDropDown)

	a.applyFormTheme(form)

	form.AddButton("Start Compression", func() {
		cfg.Operation = copier.OpCopy
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		// For standalone compression, let ensureCompressionExtension add the proper suffix
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()
		cfg.Compression = "compress"
		_, cfg.CompressionType = compressionTypeDropDown.GetCurrentOption()
		_, level := compressionLevelDropDown.GetCurrentOption()
		if level == "N/A" {
			cfg.CompressionLevel = "Good"
		} else {
			cfg.CompressionLevel = level
		}

		// If input is a directory, CreateArchive path logic will enforce extension.
		a.runOperation(cfg)
	})

	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Standalone Compression")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}

// showDecompressForm displays a standalone decompression form.
func (a *App) showDecompressForm() {
	pageName := "decompressForm"
	cfg := copier.NewConfig()
	cfg.SetUIMode(true)

	form := tview.NewForm().
		AddInputField("Archive Input", "", 40, nil, nil).
		AddInputField("Output Directory", "", 40, nil, nil)

	compressionTypeDropDown := tview.NewDropDown().
		SetLabel("Archive Type").
		SetOptions([]string{"auto", "tar.gz", "tar.xz", "zip"}, nil)

	a.applyFormTheme(form)
	form.AddFormItem(compressionTypeDropDown)

	form.AddButton("Start Extraction", func() {
		cfg.Operation = copier.OpCopy
		cfg.Input = form.GetFormItem(0).(*tview.InputField).GetText()
		cfg.Output = form.GetFormItem(1).(*tview.InputField).GetText()
		cfg.Compression = "decompress"
		_, typ := compressionTypeDropDown.GetCurrentOption()
		if typ == "auto" {
			cfg.Compression = "auto"
			cfg.CompressionType = ""
		} else {
			cfg.CompressionType = typ
		}
		a.runOperation(cfg)
	})

	form.AddButton("Back", func() { a.goBack(pageName) })

	form.SetBorder(true).SetTitle("Standalone Decompression")
	form.SetInputCapture(func(event *tcell.EventKey) *tcell.EventKey {
		if event.Key() == tcell.KeyEscape {
			a.goBack(pageName)
			return nil
		}
		return event
	})

	a.pages.AddPage(pageName, form, true, true)
}
