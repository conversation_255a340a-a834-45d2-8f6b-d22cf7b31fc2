package ui

import (
	"strconv"
	"strings"
)

// titleCase converts a string to title case (simple implementation)
func titleCase(s string) string {
	if len(s) == 0 {
		return s
	}
	return strings.ToUpper(s[:1]) + strings.ToLower(s[1:])
}

// parseBlockSize parses a block size string with optional K/M/G suffix
func parseBlockSize(s string) (int, error) {
	s = strings.ToUpper(strings.TrimSpace(s))
	mult := 1
	suffix := ""
	if strings.HasSuffix(s, "K") || strings.HasSuffix(s, "M") || strings.HasSuffix(s, "G") {
		suffix = s[len(s)-1:]
		s = s[:len(s)-1]
	}
	val, err := strconv.Atoi(s)
	if err != nil {
		return 0, err
	}
	switch suffix {
	case "K":
		mult = 1024
	case "M":
		mult = 1024 * 1024
	case "G":
		mult = 1024 * 1024 * 1024
	}
	return val * mult, nil
}
