package ui

import (
	"fmt"

	"github.com/gdamore/tcell/v2"
	"github.com/rivo/tview"
)

// AppTheme defines the complete color scheme for the application
type AppTheme struct {
	// Background colors
	PrimaryBg   tcell.Color
	SecondaryBg tcell.Color
	ModalBg     tcell.Color

	// Text colors
	PrimaryText   tcell.Color
	SecondaryText tcell.Color
	AccentText    tcell.Color

	// Border colors
	BorderColor        tcell.Color
	FocusedBorderColor tcell.Color

	// Status colors
	SuccessColor tcell.Color
	ErrorColor   tcell.Color
	WarningColor tcell.Color
	InfoColor    tcell.Color

	// Progress colors
	ProgressFill  tcell.Color
	ProgressEmpty tcell.Color
	ProgressText  tcell.Color

	// Special UI colors
	TitleColor    tcell.Color
	ButtonColor   tcell.Color
	SelectedColor tcell.Color
}

// DefaultTheme returns the default dark teal-blue theme
func DefaultTheme() *AppTheme {
	return &AppTheme{
		// Dark teal-blue background scheme
		PrimaryBg:   tcell.NewRGBColor(15, 25, 35), // Very dark teal-blue
		SecondaryBg: tcell.NewRGBColor(25, 35, 45), // Slightly lighter teal-blue
		ModalBg:     tcell.NewRGBColor(35, 45, 55), // Modal background

		// Text colors for good contrast
		PrimaryText:   tcell.ColorWhite,
		SecondaryText: tcell.NewRGBColor(180, 190, 200), // Light gray
		AccentText:    tcell.NewRGBColor(100, 200, 255), // Light blue accent

		// Border colors
		BorderColor:        tcell.NewRGBColor(70, 80, 90),    // Subtle border
		FocusedBorderColor: tcell.NewRGBColor(100, 200, 255), // Bright blue when focused

		// Status colors
		SuccessColor: tcell.NewRGBColor(50, 200, 50),   // Green
		ErrorColor:   tcell.NewRGBColor(255, 80, 80),   // Red
		WarningColor: tcell.NewRGBColor(255, 200, 50),  // Yellow
		InfoColor:    tcell.NewRGBColor(100, 200, 255), // Light blue

		// Progress colors
		ProgressFill:  tcell.NewRGBColor(50, 150, 200), // Teal-blue progress
		ProgressEmpty: tcell.NewRGBColor(60, 70, 80),   // Dark empty progress
		ProgressText:  tcell.ColorWhite,

		// Special UI colors
		TitleColor:    tcell.NewRGBColor(100, 200, 255), // Light blue for titles
		ButtonColor:   tcell.NewRGBColor(70, 130, 180),  // Steel blue for buttons
		SelectedColor: tcell.NewRGBColor(50, 100, 150),  // Selection highlight
	}
}

// ApplyTheme applies the theme globally to tview
func (theme *AppTheme) ApplyTheme() {
	// Set global tview styles
	tview.Styles = tview.Theme{
		PrimitiveBackgroundColor:    theme.PrimaryBg,
		ContrastBackgroundColor:     theme.SecondaryBg,
		MoreContrastBackgroundColor: theme.ModalBg,
		BorderColor:                 theme.BorderColor,
		TitleColor:                  theme.TitleColor,
		GraphicsColor:               theme.AccentText,
		PrimaryTextColor:            theme.PrimaryText,
		SecondaryTextColor:          theme.SecondaryText,
		TertiaryTextColor:           theme.SecondaryText,
		InverseTextColor:            theme.PrimaryBg,
		ContrastSecondaryTextColor:  theme.SecondaryText,
	}
}

// colorToHex converts a tcell.Color to a hex string for tview markup
func (theme *AppTheme) colorToHex(color tcell.Color) string {
	r, g, b := color.RGB()
	return fmt.Sprintf("#%02x%02x%02x", r, g, b)
}
