package ui

import (
	"fmt"

	"github.com/rivo/tview"
)

// showDetailedError displays an error modal with detailed error information
func (a *App) showDetailedError(operation string, err error) {
	pageName := "detailedError"
	modal := tview.NewModal().
		SetText(fmt.Sprintf("Error during %s:\n\n%v", operation, err)).
		AddButtons([]string{"OK"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			a.pages.RemovePage(pageName)
			a.app.ForceDraw()
		})
	a.pages.AddPage(pageName, modal, true, true)
}

// showConfirmation displays a confirmation dialog with Yes/No buttons
func (a *App) showConfirmation(message string, onConfirm func()) {
	pageName := "confirmModal"
	modal := tview.NewModal().
		SetText(message).
		AddButtons([]string{"Yes", "No"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			a.pages.RemovePage(pageName)
			a.app.ForceDraw()
			if buttonLabel == "Yes" {
				onConfirm()
			}
		})
	a.pages.AddPage(pageName, modal, true, true)
}

// showFileExistsDialog displays a dialog when a file already exists with options to overwrite, append, or cancel
func (a *App) showFileExistsDialog(filePath string, onAction func(string)) {
	pageName := "fileExistsModal"
	message := fmt.Sprintf("File '%s' already exists.\nWhat would you like to do?", filePath)
	modal := tview.NewModal().
		SetText(message).
		AddButtons([]string{"Overwrite", "Append", "Cancel"}).
		SetDoneFunc(func(buttonIndex int, buttonLabel string) {
			a.pages.RemovePage(pageName)
			a.app.ForceDraw()
			switch buttonLabel {
			case "Overwrite":
				onAction("overwrite")
			case "Append":
				onAction("append")
			case "Cancel":
				onAction("cancel")
			}
		})
	a.pages.AddPage(pageName, modal, true, true)
}
