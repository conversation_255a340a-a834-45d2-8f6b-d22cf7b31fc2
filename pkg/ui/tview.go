package ui

import (
	"fmt"
	"os"

	"github.com/rivo/tview"
)

// App is the main TUI application structure.
type App struct {
	app   *tview.Application
	pages *tview.Pages
	theme *AppTheme
}

// New<PERSON><PERSON> creates and initializes the TUI application.
func NewApp() *App {
	theme := DefaultTheme()
	theme.ApplyTheme()

	return &App{
		app:   tview.NewApplication(),
		pages: tview.NewPages(),
		theme: theme,
	}
}

// Run starts the application's main loop.
func (a *App) Run() error {
	mainMenu := a.createMainMenu()

	asciiArt := ` ________  _______   ___       ___       ________     
|\   __  \|\  ____\ |\  \     |\  \     |\   __  \    
\ \  \|\ /\ \ \___| \ \  \    \ \  \    \ \  \|\  \   
 \ \   __  \ \   __\ \ \  \    \ \  \    \ \   __  \  
  \ \  \|\  \ \  \_|__\ \  \____\ \  \____\ \  \ \  \ 
   \ \_______\ \_______\ \_______\ \_______\ \__\ \__\
    \|_______|\|_______|\|_______|\|_______|\|__|\|__|`

	subtitle := `------------------Advanced Data Tools-----------------`
	fullTitle := fmt.Sprintf("%s\n\n%s", asciiArt, subtitle)

	titleView := tview.NewTextView().
		SetText(fullTitle).
		SetDynamicColors(true).
		SetTextColor(a.theme.TitleColor).
		SetTextAlign(tview.AlignCenter)

	mainPageFlex := tview.NewFlex().SetDirection(tview.FlexRow).
		AddItem(titleView, 10, 0, false).
		AddItem(mainMenu, 0, 1, true)

	mainPageFlex.SetBorder(true)

	frame := tview.NewFrame(mainPageFlex)

	helpText := fmt.Sprintf("[%s]Use arrows, Enter to select, Esc to go back", a.theme.colorToHex(a.theme.InfoColor))
	sudoStatus := fmt.Sprintf("[%s]Running as root.", a.theme.colorToHex(a.theme.SuccessColor))
	if os.Getuid() != 0 {
		sudoStatus = fmt.Sprintf("[%s]Running as user. Restart with 'sudo' for device access.", a.theme.colorToHex(a.theme.WarningColor))
	}
	frame.AddText(helpText, false, tview.AlignLeft, a.theme.PrimaryText)
	frame.AddText(sudoStatus, false, tview.AlignLeft, a.theme.PrimaryText)

	a.pages.AddPage("main", frame, true, true)
	a.app.SetRoot(a.pages, true).SetFocus(mainPageFlex)

	a.app.EnableMouse(false)

	return a.app.Run()
}

// goBack is a helper to return to the main menu.
func (a *App) goBack(pageToHide string) {
	a.pages.RemovePage(pageToHide)
	a.pages.SwitchToPage("main")
	a.app.Sync()
	a.app.ForceDraw()
}

// switchToPage is a helper to cleanly switch between pages
func (a *App) switchToPage(pageName string) {
	a.pages.SwitchToPage(pageName)
	a.app.ForceDraw()
}

// createMainMenu builds the main navigation list.
func (a *App) createMainMenu() *tview.List {
	list := tview.NewList().
		SetWrapAround(false).
		// By adding a newline at the START of the secondary text, we create a
		// blank line between the main item and its description.
		// A newline at the END creates space between this item and the next.
		AddItem("Copy", "\nCopy files, directories, or devices\n", 'c', a.showCopyForm).
		AddItem("Compress/Archive", "\nCompress a file or directory into zip, tar.gz, or tar.xz\n", 'p', a.showCompressForm).
		AddItem("Decompress/Extract", "\nExtract zip, tar.gz, or tar.xz archives\n", 'd', a.showDecompressForm).
		AddItem("Verify", "\nVerify files by comparing source and destination\n", 'v', a.showVerifyForm).
		AddItem("Wipe", "\nSecurely wipe a device or file\n", 'w', a.showWipeForm).
		AddItem("List Devices", "\nShow available storage devices (display only)\n", 'l', a.showDeviceList).
		// The last item doesn't need extra space below it, but it needs space above.
		AddItem("Quit", "\nExit Bella", 'q', func() { a.app.Stop() })

	// The CORRECT way to add padding/indentation to a List.
	// This pads the content *inside* the primitive's border.
	// Top, Bottom, Left, Right
	list.SetBorderPadding(2, 0, 4, 0)

	return list
}
