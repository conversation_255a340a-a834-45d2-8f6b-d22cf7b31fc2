package copier

import (
	"archive/tar"
	"archive/zip"
	"compress/gzip"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"

	"bella/pkg/progress"

	"github.com/ulikunitz/xz"
)

// tarGzCloser handles closing both tar and gzip writers in the correct order
type tarGzCloser struct {
	tw *tar.Writer
	gw *gzip.Writer
}

func (c *tarGzCloser) Close() error {
	if err := c.tw.Close(); err != nil {
		c.gw.Close() // Try to close gzip writer even if tar fails
		return err
	}
	return c.gw.Close()
}

// tarXzCloser handles closing both tar and xz writers in the correct order
type tarXzCloser struct {
	tw  *tar.Writer
	xzw *xz.Writer
}

func (c *tarXzCloser) Close() error {
	if err := c.tw.Close(); err != nil {
		c.xzw.Close() // Try to close xz writer even if tar fails
		return err
	}
	return c.xzw.Close()
}

// mapCompressionLevel maps user-friendly names to compression constants.
func mapCompressionLevel(level string, compType string) (int, error) {
	var l int
	switch strings.ToLower(level) {
	case "good":
		l = 6
	case "better":
		l = 7
	case "best":
		l = 9
	default:
		l = 6
	}

	if compType == "gzip" {
		if l > gzip.BestCompression {
			l = gzip.BestCompression
		}
	}
	return l, nil
}

// ensureCompressionExtension ensures the output path has the correct extension for the compression type
func ensureCompressionExtension(outputPath, compressionType string) string {
	var expectedExt string

	switch compressionType {
	case "gzip":
		expectedExt = ".gz"
	case "xz":
		expectedExt = ".xz"
	case "zip":
		expectedExt = ".zip"
	case "tar.gz":
		expectedExt = ".tar.gz"
	case "tar.xz":
		expectedExt = ".tar.xz"
	default:
		return outputPath // No extension needed for unknown types
	}

	// Check if the output already has the correct extension
	lowerPath := strings.ToLower(outputPath)
	if strings.HasSuffix(lowerPath, expectedExt) {
		return outputPath
	}

	// Also check for shorthand extensions
	if compressionType == "tar.gz" && strings.HasSuffix(lowerPath, ".tgz") {
		return outputPath
	}
	if compressionType == "tar.xz" && strings.HasSuffix(lowerPath, ".txz") {
		return outputPath
	}

	// Check if it has any compression extension and replace it
	// (lowerPath already defined above)
	if strings.HasSuffix(lowerPath, ".gz") ||
		strings.HasSuffix(lowerPath, ".xz") ||
		strings.HasSuffix(lowerPath, ".zip") ||
		strings.HasSuffix(lowerPath, ".tar.gz") ||
		strings.HasSuffix(lowerPath, ".tar.xz") {
		// Remove existing compression extension and add the correct one
		if strings.HasSuffix(lowerPath, ".tar.gz") {
			outputPath = outputPath[:len(outputPath)-7]
		} else if strings.HasSuffix(lowerPath, ".tar.xz") {
			outputPath = outputPath[:len(outputPath)-7]
		} else if strings.HasSuffix(lowerPath, ".gz") {
			outputPath = outputPath[:len(outputPath)-3]
		} else if strings.HasSuffix(lowerPath, ".xz") {
			outputPath = outputPath[:len(outputPath)-3]
		} else if strings.HasSuffix(lowerPath, ".zip") {
			outputPath = outputPath[:len(outputPath)-4]
		}
	}

	return outputPath + expectedExt
}

// appendTempFileToFinal appends the content of a temporary file to the final output file
func appendTempFileToFinal(tempPath, finalPath string) error {
	tempFile, err := os.Open(tempPath)
	if err != nil {
		return fmt.Errorf("failed to open temporary file for append: %w", err)
	}
	defer tempFile.Close()

	finalFile, err := os.OpenFile(finalPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("failed to open final file for append: %w", err)
	}
	defer finalFile.Close()

	_, err = io.Copy(finalFile, tempFile)
	if err != nil {
		return fmt.Errorf("failed to append temp file content: %w", err)
	}

	return finalFile.Sync()
}

// appendCompressedFile compresses and appends the content of a temporary file to the final output file
func appendCompressedFile(tempPath, finalPath string, cfg *Config) error {
	tempFile, err := os.Open(tempPath)
	if err != nil {
		return fmt.Errorf("failed to open temporary file for compression: %w", err)
	}
	defer tempFile.Close()

	finalFile, err := os.OpenFile(finalPath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		return fmt.Errorf("failed to open final file for compressed append: %w", err)
	}
	defer finalFile.Close()

	var writer io.Writer = finalFile
	var closer io.Closer

	switch cfg.CompressionType {
	case "gzip":
		level, err := mapCompressionLevel(cfg.CompressionLevel, "gzip")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		gw, err := gzip.NewWriterLevel(writer, level)
		if err != nil {
			return fmt.Errorf("failed to create gzip writer for append: %w", err)
		}
		writer = gw
		closer = gw
	case "xz":
		// Use WriterConfig for xz compression levels in append mode too
		config := xz.WriterConfig{}
		level, err := mapCompressionLevel(cfg.CompressionLevel, "xz")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		// Map compression level to xz properties
		if level >= 9 {
			config.DictCap = 64 * 1024 * 1024 // 64MB for best compression
		} else if level >= 7 {
			config.DictCap = 16 * 1024 * 1024 // 16MB for better compression
		} else {
			config.DictCap = 8 * 1024 * 1024 // 8MB for good compression
		}
		xzw, err := config.NewWriter(writer)
		if err != nil {
			return fmt.Errorf("failed to create xz writer for append: %w", err)
		}
		writer = xzw
		closer = xzw
	case "tar.gz":
		// Note: Appending to tar archives is complex and not commonly supported
		return fmt.Errorf("tar.gz compression append is not supported for individual files")
	case "tar.xz":
		// Note: Appending to tar archives is complex and not commonly supported
		return fmt.Errorf("tar.xz compression append is not supported for individual files")
	case "zip":
		// Note: ZIP append is complex and not commonly used for single files
		// For now, we'll treat it as an error
		return fmt.Errorf("ZIP compression append is not supported for individual files")
	default:
		// No compression, write directly to output
	}

	if closer != nil {
		defer closer.Close()
	}

	_, err = io.Copy(writer, tempFile)
	if err != nil {
		return fmt.Errorf("failed to compress and append temp file content: %w", err)
	}

	return finalFile.Sync()
}

// compressFile compresses an input file to an output file
func compressFile(inputPath, outputPath string, cfg *Config) error {
	in, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input for compression: %w", err)
	}
	defer in.Close()

	out, err := createFileWithMetadata(inputPath, outputPath, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, cfg.PreserveAttributes)
	if err != nil {
		return fmt.Errorf("failed to create compressed output: %w", err)
	}
	defer out.Close()

	info, err := in.Stat()
	if err != nil {
		return fmt.Errorf("failed to get file info for compression: %w", err)
	}
	totalSize := info.Size()

	var writer io.Writer = out
	var closer io.Closer

	switch cfg.CompressionType {
	case "gzip":
		level, err := mapCompressionLevel(cfg.CompressionLevel, "gzip")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		gw, err := gzip.NewWriterLevel(writer, level)
		if err != nil {
			return fmt.Errorf("failed to create gzip writer: %w", err)
		}
		writer = gw
		closer = gw
	case "xz":
		// Use WriterConfig to support compression levels for xz
		config := xz.WriterConfig{}
		level, err := mapCompressionLevel(cfg.CompressionLevel, "xz")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		// Map compression level to xz properties
		if level >= 9 {
			config.DictCap = 64 * 1024 * 1024 // 64MB for best compression
		} else if level >= 7 {
			config.DictCap = 16 * 1024 * 1024 // 16MB for better compression
		} else {
			config.DictCap = 8 * 1024 * 1024 // 8MB for good compression
		}
		xzw, err := config.NewWriter(writer)
		if err != nil {
			return fmt.Errorf("failed to create xz writer: %w", err)
		}
		writer = xzw
		closer = xzw
	case "tar.gz":
		// For single file tar.gz compression, create a proper tar archive with gzip compression
		level, err := mapCompressionLevel(cfg.CompressionLevel, "gzip")
		if err != nil {
			return fmt.Errorf("failed to map compression level: %w", err)
		}
		gw, err := gzip.NewWriterLevel(writer, level)
		if err != nil {
			return fmt.Errorf("failed to create gzip writer: %w", err)
		}
		tw := tar.NewWriter(gw)

		// Create tar header for the single file
		originalFilename := filepath.Base(cfg.Input)
		header := &tar.Header{
			Name: originalFilename,
			Mode: 0644,
			Size: totalSize,
		}
		if err := tw.WriteHeader(header); err != nil {
			gw.Close()
			return fmt.Errorf("failed to write tar header: %w", err)
		}

		writer = tw
		closer = &tarGzCloser{tw: tw, gw: gw}
	case "tar.xz":
		// For single file tar.xz compression, create a proper tar archive with xz compression
		xzw, err := xz.NewWriter(writer)
		if err != nil {
			return fmt.Errorf("failed to create xz writer: %w", err)
		}
		tw := tar.NewWriter(xzw)

		// Create tar header for the single file
		originalFilename := filepath.Base(cfg.Input)
		header := &tar.Header{
			Name: originalFilename,
			Mode: 0644,
			Size: totalSize,
		}
		if err := tw.WriteHeader(header); err != nil {
			xzw.Close()
			return fmt.Errorf("failed to write tar header: %w", err)
		}

		writer = tw
		closer = &tarXzCloser{tw: tw, xzw: xzw}
	case "zip":
		// For single file zip compression, we need to create a zip archive with one file
		zw := zip.NewWriter(writer)
		// Use the original input filename from cfg.Input, not the temp file path
		originalFilename := filepath.Base(cfg.Input)
		// Remove any existing compression extension for the internal filename
		if filepath.Ext(originalFilename) == ".gz" || filepath.Ext(originalFilename) == ".xz" {
			originalFilename = originalFilename[:len(originalFilename)-len(filepath.Ext(originalFilename))]
		}
		zipFile, err := zw.Create(originalFilename)
		if err != nil {
			return fmt.Errorf("failed to create zip file entry: %w", err)
		}
		writer = zipFile
		closer = zw
	default:
		// No compression, write directly to output
	}

	if closer != nil {
		defer closer.Close()
	}

	reporter := progress.NewReporter("Compressing", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize)
	}

	// Determine if we should use parallel I/O optimization
	// Use parallel I/O for large files (>10MB) and when we have multiple cores
	useParallelIO := totalSize > 10*1024*1024 && runtime.NumCPU() > 1

	var compressionErr error
	if useParallelIO {
		compressionErr = compressFileWithParallelIO(in, writer, totalSize, cfg, reporter)
	} else {
		compressionErr = compressFileSequential(in, writer, totalSize, cfg, reporter)
	}

	if compressionErr != nil {
		return compressionErr
	}

	// Calculate compression ratio if this is a compression operation
	if cfg.Progress && (cfg.CompressionType != "" && cfg.CompressionType != "none") {
		compressedSize, sizeErr := getFileSize(cfg.Output)
		if sizeErr == nil {
			reporter.FinishCompression(totalSize, totalSize, compressedSize)
		} else {
			reporter.Finish(totalSize)
		}
	} else if cfg.Progress {
		reporter.Finish(totalSize)
	}

	return nil
}

// getFileSize returns the size of a file in bytes
func getFileSize(filePath string) (int64, error) {
	info, err := os.Stat(filePath)
	if err != nil {
		return 0, err
	}
	return info.Size(), nil
}

// compressFileSequential performs traditional sequential compression
func compressFileSequential(in io.Reader, writer io.Writer, totalSize int64, cfg *Config, reporter *progress.Reporter) error {
	// Use a larger buffer for better performance with large files
	bufferSize := cfg.BlockSize
	if bufferSize < 64*1024 {
		bufferSize = 64 * 1024 // Minimum 64KB buffer
	}
	if bufferSize > 1024*1024 {
		bufferSize = 1024 * 1024 // Maximum 1MB buffer to avoid excessive memory usage
	}

	buf := make([]byte, bufferSize)
	var written int64
	for {
		n, err := in.Read(buf)
		if n > 0 {
			if _, wErr := writer.Write(buf[:n]); wErr != nil {
				return fmt.Errorf("compression write error: %w", wErr)
			}
			written += int64(n)
			if cfg.Progress {
				reporter.Update(written, totalSize)
			}
		}
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("compression read error: %w", err)
		}
	}

	// Note: Finish is called by the parent function to include compression ratio
	return nil
}

// compressFileWithParallelIO performs compression with parallel I/O optimization
func compressFileWithParallelIO(in io.Reader, writer io.Writer, totalSize int64, cfg *Config, reporter *progress.Reporter) error {
	// Use double buffering to overlap I/O and compression
	// This approach keeps the compression algorithm sequential but optimizes I/O

	bufferSize := cfg.BlockSize
	if bufferSize < 256*1024 {
		bufferSize = 256 * 1024 // Minimum 256KB buffer for parallel I/O
	}
	if bufferSize > 2*1024*1024 {
		bufferSize = 2 * 1024 * 1024 // Maximum 2MB buffer
	}

	// Create two buffers for double buffering
	buf1 := make([]byte, bufferSize)
	buf2 := make([]byte, bufferSize)

	// Channels for coordinating read/write operations
	readChan := make(chan readResult, 2)
	writeChan := make(chan writeRequest, 2)

	var wg sync.WaitGroup
	var written int64

	// Start reader goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()
		defer close(readChan)

		useFirstBuffer := true
		for {
			var currentBuf []byte
			if useFirstBuffer {
				currentBuf = buf1
			} else {
				currentBuf = buf2
			}

			n, err := in.Read(currentBuf)
			readChan <- readResult{data: currentBuf[:n], err: err}

			if err != nil {
				break
			}

			// Switch buffers
			useFirstBuffer = !useFirstBuffer
		}
	}()

	// Start writer goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()

		for req := range writeChan {
			n, err := writer.Write(req.data)
			req.result <- writeResult{bytesWritten: n, err: err}
		}
	}()

	// Main compression loop with parallel I/O
	for readRes := range readChan {
		if readRes.err != nil && readRes.err != io.EOF {
			close(writeChan)
			wg.Wait()
			return fmt.Errorf("parallel I/O read error: %w", readRes.err)
		}

		if len(readRes.data) > 0 {
			// Send write request
			resultChan := make(chan writeResult, 1)
			writeChan <- writeRequest{data: readRes.data, result: resultChan}

			// Wait for write to complete
			writeRes := <-resultChan
			if writeRes.err != nil {
				close(writeChan)
				wg.Wait()
				return fmt.Errorf("parallel I/O write error: %w", writeRes.err)
			}

			written += int64(writeRes.bytesWritten)
			if cfg.Progress {
				reporter.Update(written, totalSize)
			}
		}

		if readRes.err == io.EOF {
			break
		}
	}

	close(writeChan)
	wg.Wait()

	// Note: Finish is called by the parent function to include compression ratio
	return nil
}

// readResult represents the result of a read operation
type readResult struct {
	data []byte
	err  error
}

// writeRequest represents a write request with a result channel
type writeRequest struct {
	data   []byte
	result chan<- writeResult
}

// writeResult represents the result of a write operation
type writeResult struct {
	bytesWritten int
	err          error
}
