package copier

import (
	"bytes"
	"crypto/rand"
	"fmt"
	"io"
	"log"
	"os"

	"bella/pkg/device"
	"bella/pkg/progress"
)

// doWipe performs secure wiping of a device or file
func doWipe(cfg *Config) error {
	out, err := os.OpenFile(cfg.Output, os.O_WRONLY, 0)
	if err != nil {
		return fmt.Errorf("failed to open output for wiping: %w", err)
	}
	defer out.Close()
	totalSize, err := device.GetDeviceSize(cfg.Output)
	if err != nil {
		log.Printf("Warning: could not determine wipe size: %v\n", err)
		totalSize = 0
	}
	buf := make([]byte, cfg.BlockSize)

	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("Wipe", cfg.ProgressChan)
	}

	for pass := 1; pass <= cfg.WipePasses; pass++ {
		if cfg.Progress {
			stageName := fmt.Sprintf("Wiping (Pass %d/%d)", pass, cfg.WipePasses)
			reporter.SetStage(stageName)
			reporter.Update(0, totalSize)
		} else {
			fmt.Fprintf(os.Stderr, "Starting pass %d of %d...\n", pass, cfg.WipePasses)
		}

		if _, err := out.Seek(0, io.SeekStart); err != nil {
			return fmt.Errorf("failed to seek to start for wipe pass %d: %w", pass, err)
		}
		var written int64 = 0
		for {
			if totalSize > 0 && written >= totalSize {
				break
			}
			if cfg.WipeMode == "random" {
				if _, err := rand.Read(buf); err != nil {
					return fmt.Errorf("failed to generate random data: %w", err)
				}
			} else {
				for i := range buf {
					buf[i] = 0
				}
			}
			n, err := out.Write(buf)
			if n > 0 {
				written += int64(n)
				if cfg.Progress {
					reporter.Update(written, totalSize)
				}
			}
			if err != nil {
				if totalSize > 0 && written >= totalSize {
					break
				}
				return fmt.Errorf("wipe write error: %w", err)
			}
		}
	}

	if cfg.Progress {
		reporter.Finish(totalSize)
	}
	return nil
}

// doVerify performs byte-by-byte verification of source and destination
func doVerify(cfg *Config) error {
	src, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open source '%s' for verification: %w", cfg.Input, err)
	}
	defer src.Close()

	if _, err := os.Stat(cfg.Output); os.IsNotExist(err) {
		return fmt.Errorf("verification failed: destination file '%s' does not exist", cfg.Output)
	}

	dst, err := os.Open(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to open destination '%s' for verification: %w", cfg.Output, err)
	}
	defer dst.Close()

	srcSize, err := device.GetDeviceSize(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to get source size for verification: %w", err)
	}

	dstStat, err := dst.Stat()
	if err != nil {
		return fmt.Errorf("failed to stat destination for verification: %w", err)
	}

	isDestDevice := (dstStat.Mode() & os.ModeDevice) != 0

	if !isDestDevice {
		dstSize, err := device.GetDeviceSize(cfg.Output)
		if err != nil {
			return fmt.Errorf("failed to get destination size for verification: %w", err)
		}
		if srcSize != dstSize {
			return fmt.Errorf("verification failed: file sizes differ (source: %d, destination: %d)", srcSize, dstSize)
		}
	} else {
		dstSize, err := device.GetDeviceSize(cfg.Output)
		if err != nil {
			return fmt.Errorf("failed to get destination size for verification: %w", err)
		}
		if srcSize > dstSize {
			return fmt.Errorf("verification failed: destination device too small (source: %d, destination: %d)", srcSize, dstSize)
		}
	}

	totalSize := srcSize
	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("Verifying", cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	srcBuf := make([]byte, cfg.BlockSize)
	dstBuf := make([]byte, cfg.BlockSize)
	var verified int64

	for {
		n, srcErr := src.Read(srcBuf)
		if srcErr != nil && srcErr != io.EOF {
			return fmt.Errorf("error reading source during verification: %w", srcErr)
		}
		if n == 0 {
			break
		}

		_, dstErr := io.ReadFull(dst, dstBuf[:n])
		if dstErr != nil {
			if dstErr == io.ErrUnexpectedEOF || dstErr == io.EOF {
				return fmt.Errorf("verification failed: destination is shorter than source at offset %d", verified)
			}
			return fmt.Errorf("error reading destination during verification: %w", dstErr)
		}

		if !bytes.Equal(srcBuf[:n], dstBuf[:n]) {
			return fmt.Errorf("verification failed: data mismatch at offset %d", verified)
		}

		verified += int64(n)
		if cfg.Progress && reporter != nil {
			reporter.Update(verified, totalSize)
		}

		if srcErr == io.EOF {
			break
		}
	}

	if cfg.Progress {
		reporter.Finish(verified)
	} else {
		fmt.Printf("Verification successful: %s verified.\n", progress.HumanizeBytes(uint64(verified)))
	}

	return nil
}
