package copier

import (
	"archive/tar"
	"archive/zip"
	"compress/gzip"
	"fmt"
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"

	"bella/pkg/progress"

	"github.com/ulikunitz/xz"
)

// copyDirectory performs a sequential, recursive copy of a directory.
func copyDirectory(cfg *Config) error {
	log.Printf("Preparing to recursively copy directory %s to %s\n", cfg.Input, cfg.Output)

	type fileJob struct {
		srcPath  string
		destPath string
		size     int64
	}
	var fileJobs []fileJob
	var totalSize int64

	err := filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err != nil {
			return err
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			return err
		}
		destPath := filepath.Join(cfg.Output, relPath)

		if d.Is<PERSON>ir() {
			if err := os.Mkdir<PERSON>ll(destPath, 0755); err != nil {
				return err
			}
			if cfg.PreserveAttributes {
				if err := applyMetadata(path, destPath); err != nil {
					log.Printf("Warning: failed to apply metadata to directory '%s': %v", destPath, err)
				}
			}
			return nil
		}

		info, err := d.Info()
		if err != nil {
			return err
		}
		job := fileJob{srcPath: path, destPath: destPath, size: info.Size()}
		fileJobs = append(fileJobs, job)
		totalSize += info.Size()
		return nil
	})
	if err != nil {
		return fmt.Errorf("failed to scan source directory: %w", err)
	}

	var reporter *progress.Reporter
	if cfg.Progress {
		reporter = progress.NewReporter("copying directory", cfg.ProgressChan)
		reporter.Update(0, totalSize)
	}

	var copiedSize int64
	for _, job := range fileJobs {
		fileCfg := *cfg
		fileCfg.Input = job.srcPath
		fileCfg.Output = job.destPath
		fileCfg.Progress = false
		fileCfg.isRecursive = true
		fileCfg.Append = false

		if err := copyFileSingleStage(&fileCfg); err != nil {
			return fmt.Errorf("failed to copy file %s: %w", job.srcPath, err)
		}

		copiedSize += job.size
		if cfg.Progress && reporter != nil {
			reporter.Update(copiedSize, totalSize)
		}
	}

	if cfg.Progress && reporter != nil {
		reporter.Update(totalSize, totalSize)
	}
	log.Println("Directory copy stage completed.")

	if cfg.Verify {
		log.Println("Starting directory verification stage.")
		if cfg.Progress && reporter != nil {
			reporter.SetStage("verifying directory")
			reporter.Update(0, totalSize)
		}

		var verifiedSize int64
		for _, job := range fileJobs {
			verifyCfg := *cfg
			verifyCfg.Input = job.srcPath
			verifyCfg.Output = job.destPath
			verifyCfg.Progress = false

			if err := doVerify(&verifyCfg); err != nil {
				return fmt.Errorf("verification failed for file '%s': %w", job.destPath, err)
			}
			verifiedSize += job.size
			if cfg.Progress && reporter != nil {
				reporter.Update(verifiedSize, totalSize)
			}
		}

		if cfg.Progress && reporter != nil {
			reporter.Finish(verifiedSize)
		}
		log.Println("Directory verification stage completed successfully.")
	} else {
		if cfg.Progress && reporter != nil {
			reporter.Finish(totalSize)
		}
	}

	return nil
}

// CreateArchive determines which archive format to use and creates it.
func CreateArchive(cfg *Config) error {
	// Ensure the output path has the correct compression extension
	cfg.Output = ensureCompressionExtension(cfg.Output, cfg.CompressionType)

	switch cfg.CompressionType {
	case "tar.gz":
		return createTarGz(cfg)
	case "tar.xz":
		return createTarXz(cfg)
	case "zip":
		return createZip(cfg)
	default:
		return fmt.Errorf("unsupported archive type for directory compression: %s", cfg.CompressionType)
	}
}

// createTarGz creates a .tar.gz archive from a source directory.
func createTarGz(cfg *Config) error {
	outFile, err := os.Create(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to create archive file: %w", err)
	}
	defer outFile.Close()

	level, err := mapCompressionLevel(cfg.CompressionLevel, "tar.gz")
	if err != nil {
		return err
	}
	gzw, err := gzip.NewWriterLevel(outFile, level)
	if err != nil {
		return fmt.Errorf("failed to create gzip writer: %w", err)
	}
	defer gzw.Close()
	tw := tar.NewWriter(gzw)
	defer tw.Close()

	var totalSize int64
	var filePaths []string
	filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err == nil && !d.IsDir() {
			info, _ := d.Info()
			totalSize += info.Size()
			filePaths = append(filePaths, path)
		}
		return nil
	})

	reporter := progress.NewReporter("Archiving (tar.gz)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize) // Show progress based on uncompressed data size
	}

	var writtenSize int64
	for _, path := range filePaths {
		info, err := os.Stat(path)
		if err != nil {
			continue
		}
		header, err := tar.FileInfoHeader(info, info.Name())
		if err != nil {
			continue
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			continue
		}
		header.Name = relPath
		if err := tw.WriteHeader(header); err != nil {
			return err
		}
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		n, err := io.Copy(tw, file)
		if err != nil {
			file.Close()
			return err
		}
		file.Close()
		writtenSize += n
		if cfg.Progress {
			reporter.Update(writtenSize, totalSize)
		}
	}

	if cfg.Progress {
		// Calculate compression ratio for tar.gz
		compressedSize, sizeErr := getFileSize(cfg.Output)
		if sizeErr == nil {
			reporter.FinishCompression(writtenSize, totalSize, compressedSize)
		} else {
			reporter.Finish(writtenSize)
		}
	}
	return nil
}

// createTarXz creates a .tar.xz archive from a source directory.
func createTarXz(cfg *Config) error {
	outFile, err := os.Create(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to create archive file: %w", err)
	}
	defer outFile.Close()

	// Use WriterConfig to support compression levels for xz
	config := xz.WriterConfig{}
	level, err := mapCompressionLevel(cfg.CompressionLevel, "xz")
	if err != nil {
		return err
	}
	// Map compression level to xz properties
	if level >= 9 {
		config.DictCap = 64 * 1024 * 1024 // 64MB for best compression
	} else if level >= 7 {
		config.DictCap = 16 * 1024 * 1024 // 16MB for better compression
	} else {
		config.DictCap = 8 * 1024 * 1024 // 8MB for good compression
	}
	xzw, err := config.NewWriter(outFile)
	if err != nil {
		return fmt.Errorf("failed to create xz writer: %w", err)
	}
	defer xzw.Close()
	tw := tar.NewWriter(xzw)
	defer tw.Close()

	var totalSize int64
	var filePaths []string
	filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err == nil && !d.IsDir() {
			info, _ := d.Info()
			totalSize += info.Size()
			filePaths = append(filePaths, path)
		}
		return nil
	})

	reporter := progress.NewReporter("Archiving (tar.xz)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize) // Show progress based on uncompressed data size
	}

	var writtenSize int64
	for _, path := range filePaths {
		info, err := os.Stat(path)
		if err != nil {
			continue
		}
		header, err := tar.FileInfoHeader(info, info.Name())
		if err != nil {
			continue
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			continue
		}
		header.Name = relPath
		if err := tw.WriteHeader(header); err != nil {
			return err
		}
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		n, err := io.Copy(tw, file)
		if err != nil {
			file.Close()
			return err
		}
		file.Close()
		writtenSize += n
		if cfg.Progress {
			reporter.Update(writtenSize, totalSize)
		}
	}

	if cfg.Progress {
		// Calculate compression ratio for tar.xz
		compressedSize, sizeErr := getFileSize(cfg.Output)
		if sizeErr == nil {
			reporter.FinishCompression(writtenSize, totalSize, compressedSize)
		} else {
			reporter.Finish(writtenSize)
		}
	}
	return nil
}

// createZip creates a .zip archive from a source directory.
func createZip(cfg *Config) error {
	outFile, err := os.Create(cfg.Output)
	if err != nil {
		return fmt.Errorf("failed to create archive file: %w", err)
	}
	defer outFile.Close()
	zw := zip.NewWriter(outFile)
	defer zw.Close()

	var totalSize int64
	var filePaths []string
	filepath.WalkDir(cfg.Input, func(path string, d os.DirEntry, err error) error {
		if err == nil && !d.IsDir() {
			info, _ := d.Info()
			totalSize += info.Size()
			filePaths = append(filePaths, path)
		}
		return nil
	})

	reporter := progress.NewReporter("Archiving (zip)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize)
	}

	var writtenSize int64
	for _, path := range filePaths {
		info, err := os.Stat(path)
		if err != nil {
			continue
		}
		header, err := zip.FileInfoHeader(info)
		if err != nil {
			continue
		}
		relPath, err := filepath.Rel(cfg.Input, path)
		if err != nil {
			continue
		}
		header.Name = relPath
		header.Method = zip.Deflate
		writer, err := zw.CreateHeader(header)
		if err != nil {
			return err
		}
		file, err := os.Open(path)
		if err != nil {
			return err
		}
		n, err := io.Copy(writer, file)
		if err != nil {
			file.Close()
			return err
		}
		file.Close()
		writtenSize += n
		if cfg.Progress {
			reporter.Update(writtenSize, totalSize)
		}
	}

	if cfg.Progress {
		// Calculate compression ratio for zip
		compressedSize, sizeErr := getFileSize(cfg.Output)
		if sizeErr == nil {
			reporter.FinishCompression(writtenSize, totalSize, compressedSize)
		} else {
			reporter.Finish(writtenSize)
		}
	}
	return nil
}

// ExtractArchive determines which archive format to extract and extracts it.
func ExtractArchive(cfg *Config) error {
	// Auto-detect archive type from file extension if not explicitly set
	archiveType := cfg.CompressionType
	if cfg.Compression == "auto" {
		if strings.HasSuffix(cfg.Input, ".tar.gz") {
			archiveType = "tar.gz"
		} else if strings.HasSuffix(cfg.Input, ".tar.xz") {
			archiveType = "tar.xz"
		} else if strings.HasSuffix(cfg.Input, ".zip") {
			archiveType = "zip"
			// TODO: 7z support temporarily disabled due to hanging issue
			// } else if strings.HasSuffix(cfg.Input, ".7z") {
			//	archiveType = "7z"
		}
	}

	switch archiveType {
	case "tar.gz":
		return extractTarGz(cfg)
	case "tar.xz":
		return extractTarXz(cfg)
	case "zip":
		return extractZip(cfg)
	// TODO: 7z support temporarily disabled due to hanging issue
	// case "7z":
	//	return extract7z(cfg)
	default:
		return fmt.Errorf("unsupported archive type for extraction: %s", archiveType)
	}
}

// extractZip extracts a .zip archive to a destination directory.
func extractZip(cfg *Config) error {
	reader, err := zip.OpenReader(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open zip file (archive may be corrupted): %w", err)
	}
	defer reader.Close()

	// Ensure output directory exists
	if err := os.MkdirAll(cfg.Output, 0755); err != nil {
		if os.IsPermission(err) {
			return fmt.Errorf("permission denied creating output directory '%s': %w", cfg.Output, err)
		}
		return fmt.Errorf("failed to create output directory '%s': %w", cfg.Output, err)
	}

	var totalSize int64
	for _, file := range reader.File {
		totalSize += int64(file.UncompressedSize64)
	}

	reporter := progress.NewReporter("Extracting (zip)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize)
	}

	var extractedSize int64
	for _, file := range reader.File {
		path := filepath.Join(cfg.Output, file.Name)

		// Security check: ensure the file path is within the output directory
		if !strings.HasPrefix(path, filepath.Clean(cfg.Output)+string(os.PathSeparator)) {
			return fmt.Errorf("invalid file path in archive: %s", file.Name)
		}

		if file.FileInfo().IsDir() {
			os.MkdirAll(path, file.FileInfo().Mode())
			continue
		}

		// Create the directories for this file
		if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
			return fmt.Errorf("failed to create directory for file %s: %w", file.Name, err)
		}

		// Check if file already exists and prompt for action
		if _, err := os.Stat(path); err == nil {
			action, promptErr := promptForFileAction(path, cfg.IsUIMode, false)
			if promptErr != nil {
				return promptErr
			}
			switch action {
			case "cancel":
				return fmt.Errorf("extraction cancelled by user")
			case "append":
				// For extraction, append doesn't make much sense, treat as overwrite
				fallthrough
			case "overwrite":
				// Continue with extraction
			default:
				return fmt.Errorf("invalid action: %s", action)
			}
		}

		fileReader, err := file.Open()
		if err != nil {
			return fmt.Errorf("failed to open file in archive: %w", err)
		}

		outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.FileInfo().Mode())
		if err != nil {
			fileReader.Close()
			return fmt.Errorf("failed to create output file: %w", err)
		}

		n, err := io.Copy(outFile, fileReader)
		fileReader.Close()
		outFile.Close()

		if err != nil {
			return fmt.Errorf("failed to extract file %s: %w", file.Name, err)
		}

		extractedSize += n
		if cfg.Progress {
			reporter.Update(extractedSize, totalSize)
		}
	}

	if cfg.Progress {
		reporter.Finish(extractedSize)
	}
	return nil
}

// extractTarGz extracts a .tar.gz archive to a destination directory.
func extractTarGz(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.gz file: %w", err)
	}
	defer file.Close()

	gzr, err := gzip.NewReader(file)
	if err != nil {
		return fmt.Errorf("failed to create gzip reader (archive may be corrupted): %w", err)
	}
	defer gzr.Close()

	return extractTar(gzr, cfg, "tar.gz")
}

// extractTarXz extracts a .tar.xz archive to a destination directory.
func extractTarXz(cfg *Config) error {
	file, err := os.Open(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open tar.xz file: %w", err)
	}
	defer file.Close()

	xzr, err := xz.NewReader(file)
	if err != nil {
		return fmt.Errorf("failed to create xz reader (archive may be corrupted): %w", err)
	}

	return extractTar(xzr, cfg, "tar.xz")
}

// extractTar extracts a tar archive from the given reader.
func extractTar(reader io.Reader, cfg *Config, archiveType string) error {
	tr := tar.NewReader(reader)

	// Ensure output directory exists
	if err := os.MkdirAll(cfg.Output, 0755); err != nil {
		if os.IsPermission(err) {
			return fmt.Errorf("permission denied creating output directory '%s': %w", cfg.Output, err)
		}
		return fmt.Errorf("failed to create output directory '%s': %w", cfg.Output, err)
	}

	reporter := progress.NewReporter(fmt.Sprintf("Extracting (%s)", archiveType), cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, 0) // Total size unknown for tar files - will show bytes processed
	}

	var extractedSize int64
	var conflictAction string // Cache user's choice for multiple conflicts
	for {
		header, err := tr.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return fmt.Errorf("failed to read tar header (archive may be corrupted): %w", err)
		}

		path := filepath.Join(cfg.Output, header.Name)

		// Security check: ensure the file path is within the output directory
		if !strings.HasPrefix(path, filepath.Clean(cfg.Output)+string(os.PathSeparator)) {
			return fmt.Errorf("invalid file path in archive: %s", header.Name)
		}

		switch header.Typeflag {
		case tar.TypeDir:
			if err := os.MkdirAll(path, os.FileMode(header.Mode)); err != nil {
				return fmt.Errorf("failed to create directory %s: %w", path, err)
			}
		case tar.TypeReg:
			// Create the directories for this file
			if err := os.MkdirAll(filepath.Dir(path), 0755); err != nil {
				return fmt.Errorf("failed to create directory for file %s: %w", header.Name, err)
			}

			// Check if file already exists and prompt for action
			if _, err := os.Stat(path); err == nil {
				// Use cached action if user already decided for previous conflicts
				if conflictAction == "" {
					action, promptErr := promptForFileAction(path, cfg.IsUIMode, false)
					if promptErr != nil {
						return promptErr
					}
					conflictAction = action // Cache the decision
				}
				switch conflictAction {
				case "cancel":
					return fmt.Errorf("extraction cancelled by user")
				case "append":
					// For extraction, append doesn't make much sense, treat as overwrite
					fallthrough
				case "overwrite":
					// Continue with extraction
				default:
					return fmt.Errorf("invalid action: %s", conflictAction)
				}
			}

			outFile, err := os.OpenFile(path, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, os.FileMode(header.Mode))
			if err != nil {
				if os.IsPermission(err) {
					return fmt.Errorf("permission denied creating file '%s': %w", path, err)
				}
				return fmt.Errorf("failed to create output file '%s': %w", path, err)
			}

			n, err := io.Copy(outFile, tr)
			outFile.Close()

			if err != nil {
				if os.IsPermission(err) {
					return fmt.Errorf("permission denied writing to file '%s': %w", path, err)
				}
				return fmt.Errorf("failed to extract file '%s': %w", header.Name, err)
			}

			extractedSize += n
			if cfg.Progress {
				// For tar files, show progress as bytes extracted (no total size available)
				reporter.Update(extractedSize, 0)
			}
		}
	}

	if cfg.Progress {
		reporter.Finish(extractedSize)
	}
	return nil
}

// TODO: extract7z temporarily disabled due to hanging issue
// extract7z extracts a .7z archive to a destination directory
/*
func extract7z(cfg *Config) error {
	reader, err := sevenzip.OpenReader(cfg.Input)
	if err != nil {
		return fmt.Errorf("failed to open 7z archive: %w", err)
	}
	defer reader.Close()

	// Calculate total size for progress reporting
	var totalSize int64
	for _, file := range reader.File {
		totalSize += int64(file.UncompressedSize)
	}

	reporter := progress.NewReporter("Extracting (7z)", cfg.ProgressChan)
	if cfg.Progress {
		reporter.Update(0, totalSize)
	}

	var extractedSize int64
	for _, file := range reader.File {
		// Create the full path for the extracted file
		destPath := filepath.Join(cfg.Output, file.Name)

		// Handle file conflicts
		if _, err := os.Stat(destPath); err == nil {
			action, err := promptForFileAction(destPath, cfg.IsUIMode, false)
			if err != nil {
				return err
			}
			switch action {
			case "cancel":
				return fmt.Errorf("extraction cancelled by user")
			case "append":
				// For extraction, append doesn't make much sense, treat as overwrite
				fallthrough
			case "overwrite":
				// Continue with extraction
			default:
				return fmt.Errorf("invalid action: %s", action)
			}
		}

		// Create directory structure
		if err := os.MkdirAll(filepath.Dir(destPath), 0755); err != nil {
			return fmt.Errorf("failed to create directory structure: %w", err)
		}

		// Extract the file
		if file.FileHeader.Mode().IsDir() {
			// Create directory
			if err := os.MkdirAll(destPath, 0755); err != nil {
				return fmt.Errorf("failed to create directory %s: %w", destPath, err)
			}
		} else {
			// Extract file content
			rc, err := file.Open()
			if err != nil {
				return fmt.Errorf("failed to open file %s in archive: %w", file.Name, err)
			}

			outFile, err := os.Create(destPath)
			if err != nil {
				rc.Close()
				return fmt.Errorf("failed to create output file %s: %w", destPath, err)
			}

			_, err = io.Copy(outFile, rc)
			rc.Close()
			outFile.Close()

			if err != nil {
				return fmt.Errorf("failed to extract file %s: %w", file.Name, err)
			}

			// Set file permissions and timestamps if available
			if file.FileHeader.Mode() != 0 {
				os.Chmod(destPath, file.FileHeader.Mode())
			}
			if !file.FileHeader.Modified.IsZero() {
				os.Chtimes(destPath, file.FileHeader.Modified, file.FileHeader.Modified)
			}
		}

		extractedSize += int64(file.UncompressedSize)
		if cfg.Progress {
			reporter.Update(extractedSize, totalSize)
		}
	}

	if cfg.Progress {
		reporter.Finish(extractedSize)
	}
	return nil
}
*/
