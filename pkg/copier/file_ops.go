package copier

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"strings"
	"syscall"
)

// promptForFileAction prompts the user when a file already exists.
// Returns "overwrite", "append", or "cancel" based on user choice.
func promptForFileAction(outputPath string, isUI bool, isArchive bool) (string, error) {
	if isUI {
		// UI mode has its own dialogs. This simply allows the operation to proceed
		// to the UI dialog logic.
		return "overwrite", nil
	}

	fmt.Printf("File '%s' already exists.\n", outputPath)
	if isArchive {
		fmt.Print("Choose action: (o)verwrite, (c)ancel [o/c]: ")
	} else {
		fmt.Print("Choose action: (o)verwrite, (a)ppend, (c)ancel [o/a/c]: ")
	}

	reader := bufio.NewReader(os.Stdin)
	response, err := reader.ReadString('\n')
	if err != nil {
		return "", fmt.Errorf("failed to read user input: %w", err)
	}

	response = strings.ToLower(strings.TrimSpace(response))
	switch response {
	case "o", "overwrite":
		return "overwrite", nil
	case "a", "append":
		if isArchive {
			return "cancel", fmt.Errorf("invalid choice for an archive: %s", response)
		}
		return "append", nil
	// Removed verify option - not applicable for compression/decompression operations
	case "c", "cancel":
		return "cancel", nil
	default:
		return "cancel", fmt.Errorf("invalid choice: %s", response)
	}
}

// isAllZeros checks if a byte slice contains only zeros.
func isAllZeros(data []byte) bool {
	for _, b := range data {
		if b != 0 {
			return false
		}
	}
	return true
}

// createFileWithMetadata creates a file and immediately applies the source file's metadata
func createFileWithMetadata(srcPath, destPath string, flags int, preserveAttributes bool) (*os.File, error) {
	var srcInfo os.FileInfo
	var err error
	if preserveAttributes {
		srcInfo, err = os.Stat(srcPath)
		if err != nil {
			return nil, fmt.Errorf("failed to stat source for metadata: %w", err)
		}
	}

	perm := os.FileMode(0666)
	if preserveAttributes && srcInfo != nil {
		perm = srcInfo.Mode()
	}

	file, err := os.OpenFile(destPath, flags, perm)
	if err != nil {
		return nil, err
	}

	if preserveAttributes && srcInfo != nil {
		if err := applyMetadata(srcPath, destPath); err != nil {
			log.Printf("Warning: failed to apply metadata to '%s': %v", destPath, err)
		}
	}

	return file, nil
}

// applyMetadata applies source file metadata (permissions, ownership) to destination file
func applyMetadata(srcPath, destPath string) error {
	info, err := os.Stat(srcPath)
	if err != nil {
		return fmt.Errorf("failed to stat source for metadata: %w", err)
	}

	if err := os.Chmod(destPath, info.Mode()); err != nil {
		log.Printf("Warning: could not set permissions on %s: %v", destPath, err)
	}

	if stat, ok := info.Sys().(*syscall.Stat_t); ok {
		uid := int(stat.Uid)
		gid := int(stat.Gid)
		if err := os.Chown(destPath, uid, gid); err != nil {
			log.Printf("Warning: could not set ownership on %s (run with sudo?): %v", destPath, err)
		}
	}

	return nil
}
