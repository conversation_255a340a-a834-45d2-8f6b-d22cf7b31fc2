#!/bin/bash

# Simple test to check auto-decompression behavior

echo "Creating test files..."
mkdir -p /tmp/simple_test_dir
echo "File 1" > /tmp/simple_test_dir/file1.txt
echo "File 2" > /tmp/simple_test_dir/file2.txt

echo "Creating ZIP..."
./bella -input /tmp/simple_test_dir -output /tmp/simple_test.zip -compression compress -compresstype zip -progress=false 2>/dev/null

echo "Testing auto-decompression..."
rm -rf /tmp/test-extract
./bella -input /tmp/simple_test.zip -output /tmp/test-extract -compression auto -progress=false 2>/dev/null

echo "Checking results..."
if [ -d "/tmp/test-extract" ]; then
    echo "✓ Created directory"
    ls -la /tmp/test-extract/
else
    echo "✗ Did not create directory"
    if [ -f "/tmp/test-extract" ]; then
        echo "Created file instead:"
        file /tmp/test-extract
        head -5 /tmp/test-extract 2>/dev/null
    fi
fi

# Cleanup
rm -rf /tmp/simple_test_dir /tmp/simple_test.zip /tmp/test-extract

echo "Done."
