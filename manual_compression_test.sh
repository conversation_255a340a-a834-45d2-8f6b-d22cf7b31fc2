#!/bin/bash

# Manual compression test without progress bars

echo "Creating test file..."
echo "This line repeats many times to test compression effectiveness. " > /tmp/test_compress.txt
for i in {1..100}; do
    echo "This line repeats many times to test compression effectiveness. Line $i" >> /tmp/test_compress.txt
done

echo "Original file created."
ls -la /tmp/test_compress.txt

echo "Testing gzip compression..."
./bella -input /tmp/test_compress.txt -output /tmp/test_compress.gz -compression compress -compresstype gzip -progress=false >/dev/null 2>&1

echo "Checking results..."
if [ -f "/tmp/test_compress.gz" ]; then
    echo "Gzip file created:"
    ls -la /tmp/test_compress.gz
else
    echo "Gzip file not created"
fi

echo "Testing xz compression..."
./bella -input /tmp/test_compress.txt -output /tmp/test_compress.xz -compression compress -compresstype xz -progress=false >/dev/null 2>&1

if [ -f "/tmp/test_compress.xz" ]; then
    echo "XZ file created:"
    ls -la /tmp/test_compress.xz
else
    echo "XZ file not created"
fi

# Cleanup
rm -f /tmp/test_compress.txt /tmp/test_compress.gz /tmp/test_compress.xz

echo "Test completed."
