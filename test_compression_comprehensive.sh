#!/bin/bash

# Comprehensive Compression Test Suite for Bella
# Tests all compression types across different operation modes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Test output directory
TEST_OUTPUT_DIR="/tmp/bella_compression_tests"
mkdir -p "$TEST_OUTPUT_DIR"

# Cleanup function
cleanup() {
    echo -e "${BLUE}Cleaning up test files...${NC}"
    rm -rf "$TEST_OUTPUT_DIR"
    rm -f /tmp/test_file_*.txt
    rm -f /tmp/device_test_*
    rm -f /tmp/dir_test_*
}

# Trap to ensure cleanup on exit
trap cleanup EXIT

# Function to print test results
print_result() {
    local test_name="$1"
    local result="$2"
    local details="$3"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$result" = "PASS" ]; then
        echo -e "${GREEN}✓ PASS${NC}: $test_name"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ FAIL${NC}: $test_name"
        if [ -n "$details" ]; then
            echo -e "  ${YELLOW}Details: $details${NC}"
        fi
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# Function to test file compression
test_file_compression() {
    local compress_type="$1"
    local extension="$2"
    
    echo -e "${BLUE}Testing file compression with $compress_type...${NC}"
    
    # Create test file
    local test_file="/tmp/test_file_${compress_type}.txt"
    echo "This is a test file for $compress_type compression testing." > "$test_file"
    echo "It contains multiple lines to ensure proper compression." >> "$test_file"
    echo "Line 3 with some additional content for better compression ratio." >> "$test_file"
    
    local output_file="$TEST_OUTPUT_DIR/test_file_${compress_type}${extension}"
    
    # Test compression
    if ./bella -input "$test_file" -output "$output_file" -compression compress -compresstype "$compress_type" >/dev/null 2>&1; then
        if [ -f "$output_file" ] && [ -s "$output_file" ]; then
            print_result "File compression ($compress_type)" "PASS"
            
            # Test decompression
            local decompressed_file="$TEST_OUTPUT_DIR/decompressed_${compress_type}.txt"
            if ./bella -input "$output_file" -output "$decompressed_file" -compression decompress >/dev/null 2>&1; then
                if [ -f "$decompressed_file" ] && cmp -s "$test_file" "$decompressed_file"; then
                    print_result "File decompression ($compress_type)" "PASS"
                else
                    print_result "File decompression ($compress_type)" "FAIL" "Decompressed file doesn't match original"
                fi
            else
                print_result "File decompression ($compress_type)" "FAIL" "Decompression command failed"
            fi
        else
            print_result "File compression ($compress_type)" "FAIL" "Output file not created or empty"
        fi
    else
        print_result "File compression ($compress_type)" "FAIL" "Compression command failed"
    fi
    
    # Cleanup
    rm -f "$test_file"
}

# Function to test device compression
test_device_compression() {
    local compress_type="$1"
    local extension="$2"
    
    echo -e "${BLUE}Testing device compression with $compress_type...${NC}"
    
    local output_file="$TEST_OUTPUT_DIR/device_test_${compress_type}${extension}"
    
    # Test device compression (using /dev/zero with limited count)
    if ./bella -input /dev/zero -output "$output_file" -compression compress -compresstype "$compress_type" -count 1024 >/dev/null 2>&1; then
        if [ -f "$output_file" ] && [ -s "$output_file" ]; then
            print_result "Device compression ($compress_type)" "PASS"
        else
            print_result "Device compression ($compress_type)" "FAIL" "Output file not created or empty"
        fi
    else
        print_result "Device compression ($compress_type)" "FAIL" "Compression command failed"
    fi
}

# Function to test directory compression
test_directory_compression() {
    local compress_type="$1"
    local extension="$2"
    
    echo -e "${BLUE}Testing directory compression with $compress_type...${NC}"
    
    # Create test directory structure
    local test_dir="$TEST_OUTPUT_DIR/test_dir_${compress_type}"
    mkdir -p "$test_dir/subdir1" "$test_dir/subdir2"
    echo "File 1 content" > "$test_dir/file1.txt"
    echo "File 2 content" > "$test_dir/file2.txt"
    echo "Subdir file 1" > "$test_dir/subdir1/subfile1.txt"
    echo "Subdir file 2" > "$test_dir/subdir2/subfile2.txt"
    
    local output_file="$TEST_OUTPUT_DIR/dir_test_${compress_type}${extension}"
    
    # Test directory compression
    if ./bella -input "$test_dir" -output "$output_file" -compression compress -compresstype "$compress_type" >/dev/null 2>&1; then
        if [ -f "$output_file" ] && [ -s "$output_file" ]; then
            print_result "Directory compression ($compress_type)" "PASS"
            
            # Test decompression
            local extract_dir="$TEST_OUTPUT_DIR/extracted_${compress_type}"
            mkdir -p "$extract_dir"
            if ./bella -input "$output_file" -output "$extract_dir" -compression decompress >/dev/null 2>&1; then
                # Check if key files exist in extracted directory
                if [ -f "$extract_dir/file1.txt" ] && [ -f "$extract_dir/subdir1/subfile1.txt" ]; then
                    print_result "Directory decompression ($compress_type)" "PASS"
                else
                    print_result "Directory decompression ($compress_type)" "FAIL" "Extracted files missing"
                fi
            else
                print_result "Directory decompression ($compress_type)" "FAIL" "Decompression command failed"
            fi
        else
            print_result "Directory compression ($compress_type)" "FAIL" "Output file not created or empty"
        fi
    else
        print_result "Directory compression ($compress_type)" "FAIL" "Compression command failed"
    fi
}

# Main test execution
echo -e "${BLUE}=== Bella Comprehensive Compression Test Suite ===${NC}"
echo -e "${BLUE}Testing all compression types across different operation modes${NC}"
echo ""

# Check if bella executable exists
if [ ! -f "./bella" ]; then
    echo -e "${RED}Error: bella executable not found in current directory${NC}"
    exit 1
fi

# Test all compression types
declare -A COMPRESSION_TYPES=(
    ["gzip"]=".gz"
    ["xz"]=".xz"
    ["zip"]=".zip"
)

for compress_type in "${!COMPRESSION_TYPES[@]}"; do
    extension="${COMPRESSION_TYPES[$compress_type]}"
    
    echo -e "${YELLOW}=== Testing $compress_type compression ===${NC}"
    
    # Test file compression
    test_file_compression "$compress_type" "$extension"
    
    # Test device compression
    test_device_compression "$compress_type" "$extension"
    
    # Test directory compression (only for zip, as tar.gz and tar.xz need special handling)
    if [ "$compress_type" = "zip" ]; then
        test_directory_compression "$compress_type" "$extension"
    fi
    
    echo ""
done

# Print final results
echo -e "${BLUE}=== Test Results Summary ===${NC}"
echo -e "Total tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "${GREEN}All tests passed! ✓${NC}"
    exit 0
else
    echo -e "${RED}Some tests failed! ✗${NC}"
    exit 1
fi
