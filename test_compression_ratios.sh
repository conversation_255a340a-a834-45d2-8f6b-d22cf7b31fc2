#!/bin/bash

# Test compression ratios to verify compression is working effectively

set -e

echo "Testing compression ratios..."

# Create a test file with repetitive content that should compress well
TEST_FILE="/tmp/compressible_test.txt"
echo "Creating test file with repetitive content..."

# Create a file with repetitive content that should compress well
for i in {1..1000}; do
    echo "This is line $i with repetitive content that should compress very well when using gzip or xz compression algorithms." >> "$TEST_FILE"
done

ORIGINAL_SIZE=$(stat -c%s "$TEST_FILE")
echo "Original file size: $ORIGINAL_SIZE bytes"

# Test gzip compression
echo "Testing gzip compression..."
./bella -input "$TEST_FILE" -output "/tmp/test_gzip.gz" -compression compress -compresstype gzip -progress=false 2>/dev/null

if [ -f "/tmp/test_gzip.gz" ]; then
    GZIP_SIZE=$(stat -c%s "/tmp/test_gzip.gz")
    GZIP_RATIO=$(echo "scale=2; $GZIP_SIZE * 100 / $ORIGINAL_SIZE" | bc)
    echo "Gzip compressed size: $GZIP_SIZE bytes (${GZIP_RATIO}% of original)"
    
    if [ "$GZIP_SIZE" -lt "$ORIGINAL_SIZE" ]; then
        echo "✓ Gzip compression is working (file size reduced)"
    else
        echo "✗ Gzip compression not working effectively (file size not reduced)"
    fi
else
    echo "✗ Gzip compression failed - file not created"
fi

# Test xz compression
echo "Testing xz compression..."
./bella -input "$TEST_FILE" -output "/tmp/test_xz.xz" -compression compress -compresstype xz -progress=false 2>/dev/null

if [ -f "/tmp/test_xz.xz" ]; then
    XZ_SIZE=$(stat -c%s "/tmp/test_xz.xz")
    XZ_RATIO=$(echo "scale=2; $XZ_SIZE * 100 / $ORIGINAL_SIZE" | bc)
    echo "XZ compressed size: $XZ_SIZE bytes (${XZ_RATIO}% of original)"
    
    if [ "$XZ_SIZE" -lt "$ORIGINAL_SIZE" ]; then
        echo "✓ XZ compression is working (file size reduced)"
    else
        echo "✗ XZ compression not working effectively (file size not reduced)"
    fi
else
    echo "✗ XZ compression failed - file not created"
fi

# Test tar.gz compression
echo "Testing tar.gz compression..."
./bella -input "$TEST_FILE" -output "/tmp/test_tar.tar.gz" -compression compress -compresstype tar.gz -progress=false 2>/dev/null

if [ -f "/tmp/test_tar.tar.gz" ]; then
    TAR_GZ_SIZE=$(stat -c%s "/tmp/test_tar.tar.gz")
    TAR_GZ_RATIO=$(echo "scale=2; $TAR_GZ_SIZE * 100 / $ORIGINAL_SIZE" | bc)
    echo "Tar.gz compressed size: $TAR_GZ_SIZE bytes (${TAR_GZ_RATIO}% of original)"
    
    if [ "$TAR_GZ_SIZE" -lt "$ORIGINAL_SIZE" ]; then
        echo "✓ Tar.gz compression is working (file size reduced)"
    else
        echo "✗ Tar.gz compression not working effectively (file size not reduced)"
    fi
else
    echo "✗ Tar.gz compression failed - file not created"
fi

# Test tar.xz compression
echo "Testing tar.xz compression..."
./bella -input "$TEST_FILE" -output "/tmp/test_tar.tar.xz" -compression compress -compresstype tar.xz -progress=false 2>/dev/null

if [ -f "/tmp/test_tar.tar.xz" ]; then
    TAR_XZ_SIZE=$(stat -c%s "/tmp/test_tar.tar.xz")
    TAR_XZ_RATIO=$(echo "scale=2; $TAR_XZ_SIZE * 100 / $ORIGINAL_SIZE" | bc)
    echo "Tar.xz compressed size: $TAR_XZ_SIZE bytes (${TAR_XZ_RATIO}% of original)"
    
    if [ "$TAR_XZ_SIZE" -lt "$ORIGINAL_SIZE" ]; then
        echo "✓ Tar.xz compression is working (file size reduced)"
    else
        echo "✗ Tar.xz compression not working effectively (file size not reduced)"
    fi
else
    echo "✗ Tar.xz compression failed - file not created"
fi

# Test ZIP compression
echo "Testing ZIP compression..."
./bella -input "$TEST_FILE" -output "/tmp/test_zip.zip" -compression compress -compresstype zip -progress=false 2>/dev/null

if [ -f "/tmp/test_zip.zip" ]; then
    ZIP_SIZE=$(stat -c%s "/tmp/test_zip.zip")
    ZIP_RATIO=$(echo "scale=2; $ZIP_SIZE * 100 / $ORIGINAL_SIZE" | bc)
    echo "ZIP compressed size: $ZIP_SIZE bytes (${ZIP_RATIO}% of original)"
    
    if [ "$ZIP_SIZE" -lt "$ORIGINAL_SIZE" ]; then
        echo "✓ ZIP compression is working (file size reduced)"
    else
        echo "✗ ZIP compression not working effectively (file size not reduced)"
    fi
else
    echo "✗ ZIP compression failed - file not created"
fi

# Cleanup
rm -f "$TEST_FILE" "/tmp/test_gzip.gz" "/tmp/test_xz.xz" "/tmp/test_tar.tar.gz" "/tmp/test_tar.tar.xz" "/tmp/test_zip.zip"

echo "Compression ratio test completed."
