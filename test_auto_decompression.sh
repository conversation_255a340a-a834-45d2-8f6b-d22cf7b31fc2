#!/bin/bash

# Test auto-decompression directory creation issue

set -e

echo "Testing auto-decompression directory creation..."

# Create test directory structure
TEST_DIR="/tmp/auto_test_source"
rm -rf "$TEST_DIR"
mkdir -p "$TEST_DIR/subdir"

echo "Creating test files..."
echo "Main file content" > "$TEST_DIR/main.txt"
echo "Subdir file content" > "$TEST_DIR/subdir/sub.txt"

echo "Test directory structure:"
find "$TEST_DIR" -type f

# Create ZIP archive
echo "Creating ZIP archive..."
./bella -input "$TEST_DIR" -output "/tmp/auto_test.zip" -compression compress -compresstype zip -progress=false >/dev/null 2>&1

if [ ! -f "/tmp/auto_test.zip" ]; then
    echo "✗ Failed to create ZIP archive"
    exit 1
fi

echo "✓ ZIP archive created"

# Test auto-decompression with different output scenarios
echo "Testing auto-decompression scenarios..."

# Scenario 1: Output to existing directory
EXTRACT_DIR1="/tmp/auto_extract_dir"
rm -rf "$EXTRACT_DIR1"
mkdir -p "$EXTRACT_DIR1"

echo "Scenario 1: Extract to existing directory"
./bella -input "/tmp/auto_test.zip" -output "$EXTRACT_DIR1" -compression auto -progress=false >/dev/null 2>&1

echo "Checking if files were extracted to directory:"
if [ -d "$EXTRACT_DIR1" ]; then
    echo "✓ Output is a directory"
    if [ -f "$EXTRACT_DIR1/main.txt" ]; then
        echo "✓ main.txt extracted correctly"
    else
        echo "✗ main.txt not found"
    fi
    if [ -f "$EXTRACT_DIR1/subdir/sub.txt" ]; then
        echo "✓ subdir/sub.txt extracted correctly"
    else
        echo "✗ subdir/sub.txt not found"
    fi
else
    echo "✗ Output is not a directory"
fi

# Scenario 2: Output to non-existing path (this might be the problematic case)
EXTRACT_PATH2="/tmp/test-extract"
rm -rf "$EXTRACT_PATH2"

echo "Scenario 2: Extract to non-existing path"
./bella -input "/tmp/auto_test.zip" -output "$EXTRACT_PATH2" -compression auto -progress=false >/dev/null 2>&1

echo "Checking what was created:"
if [ -d "$EXTRACT_PATH2" ]; then
    echo "✓ Created directory: $EXTRACT_PATH2"
    if [ -f "$EXTRACT_PATH2/main.txt" ]; then
        echo "✓ main.txt extracted correctly"
    else
        echo "✗ main.txt not found"
    fi
    if [ -f "$EXTRACT_PATH2/subdir/sub.txt" ]; then
        echo "✓ subdir/sub.txt extracted correctly"
    else
        echo "✗ subdir/sub.txt not found"
    fi
elif [ -f "$EXTRACT_PATH2" ]; then
    echo "✗ Created file instead of directory: $EXTRACT_PATH2"
    echo "File contents:"
    head -5 "$EXTRACT_PATH2" 2>/dev/null || echo "Cannot read file"
else
    echo "✗ Nothing was created at: $EXTRACT_PATH2"
fi

# Scenario 3: Single file ZIP auto-decompression
echo "Scenario 3: Single file ZIP auto-decompression"
SINGLE_FILE="/tmp/single_auto_test.txt"
echo "Single file content for auto test" > "$SINGLE_FILE"

./bella -input "$SINGLE_FILE" -output "/tmp/single_auto.zip" -compression compress -compresstype zip -progress=false >/dev/null 2>&1

if [ -f "/tmp/single_auto.zip" ]; then
    echo "✓ Single file ZIP created"
    
    # Test auto-decompression of single file
    SINGLE_EXTRACT="/tmp/single_auto_extracted"
    rm -rf "$SINGLE_EXTRACT"
    
    ./bella -input "/tmp/single_auto.zip" -output "$SINGLE_EXTRACT" -compression auto -progress=false >/dev/null 2>&1
    
    if [ -f "$SINGLE_EXTRACT" ]; then
        echo "✓ Single file extracted as file"
        if grep -q "Single file content for auto test" "$SINGLE_EXTRACT"; then
            echo "✓ Single file content is correct"
        else
            echo "✗ Single file content is incorrect"
        fi
    elif [ -d "$SINGLE_EXTRACT" ]; then
        echo "? Single file extracted as directory"
        find "$SINGLE_EXTRACT" -type f
    else
        echo "✗ Single file extraction failed"
    fi
else
    echo "✗ Single file ZIP creation failed"
fi

echo "Listing all created files/directories:"
ls -la /tmp/auto_* /tmp/test-extract* /tmp/single_auto* 2>/dev/null || echo "No files found"

# Cleanup
rm -rf "$TEST_DIR" "$EXTRACT_DIR1" "$EXTRACT_PATH2" "/tmp/auto_test.zip" "$SINGLE_FILE" "/tmp/single_auto.zip" "$SINGLE_EXTRACT"

echo "Auto-decompression test completed."
