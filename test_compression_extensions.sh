#!/bin/bash

# Test script to verify compression extension functionality
# This script tests the automatic extension appending feature

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Testing Compression Extension Functionality ===${NC}"

# Create a test file
TEST_FILE="/tmp/test_video.mkv"
echo "This is a test file simulating a video file" > "$TEST_FILE"
echo "It contains some content to compress" >> "$TEST_FILE"

# Test cases for compression extension handling
declare -A TEST_CASES=(
    ["gzip,video.mkv"]="video.mkv.gz"
    ["xz,video.mkv"]="video.mkv.xz"
    ["zip,video.mkv"]="video.mkv.zip"
    ["tar.gz,video.mkv"]="video.mkv.tar.gz"
    ["tar.xz,video.mkv"]="video.mkv.tar.xz"
    ["gzip,video.mkv.gz"]="video.mkv.gz"
    ["xz,video.mkv.xz"]="video.mkv.xz"
    ["zip,video.mkv.zip"]="video.mkv.zip"
)

echo "Test file created: $TEST_FILE"
echo ""

# Function to test compression with expected extension
test_compression() {
    local compression_type="$1"
    local input_output="$2"
    local expected_output="$3"
    
    echo -e "${BLUE}Testing: $compression_type compression${NC}"
    echo "Input/Output: $input_output"
    echo "Expected final output: $expected_output"
    
    # Try to build first (if it fails, we'll note it)
    if ! go build -o bella_test 2>/dev/null; then
        echo -e "${RED}Build failed - skipping runtime test${NC}"
        return 1
    fi
    
    # Test the compression
    if ./bella_test -input "$TEST_FILE" -output "$input_output" -compression compress -compresstype "$compression_type" 2>/dev/null; then
        if [ -f "$expected_output" ]; then
            echo -e "${GREEN}✓ SUCCESS: File created with correct extension: $expected_output${NC}"
            rm -f "$expected_output"
            return 0
        else
            echo -e "${RED}✗ FAILED: Expected file not found: $expected_output${NC}"
            # List what files were actually created
            echo "Files in /tmp matching pattern:"
            ls -la /tmp/video.mkv* 2>/dev/null || echo "No matching files found"
            return 1
        fi
    else
        echo -e "${RED}✗ FAILED: Compression command failed${NC}"
        return 1
    fi
}

# Run tests
TOTAL_TESTS=0
PASSED_TESTS=0

for test_case in "${!TEST_CASES[@]}"; do
    IFS=',' read -r compression_type input_output <<< "$test_case"
    expected_output="${TEST_CASES[$test_case]}"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if test_compression "$compression_type" "$input_output" "$expected_output"; then
        PASSED_TESTS=$((PASSED_TESTS + 1))
    fi
    
    echo ""
done

# Cleanup
rm -f "$TEST_FILE"
rm -f bella_test
rm -f /tmp/video.mkv*

# Summary
echo -e "${BLUE}=== Test Summary ===${NC}"
echo "Total tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $((TOTAL_TESTS - PASSED_TESTS))${NC}"

if [ $PASSED_TESTS -eq $TOTAL_TESTS ]; then
    echo -e "${GREEN}All tests passed! ✓${NC}"
    exit 0
else
    echo -e "${RED}Some tests failed! ✗${NC}"
    exit 1
fi
