#!/bin/bash

# Test automatic file extension addition for compression

set -e

echo "Testing automatic file extension addition..."

# Create test file
TEST_FILE="/tmp/extension_test.txt"
echo "Test content for extension testing" > "$TEST_FILE"

# Test cases: input output name -> expected output file
declare -A TEST_CASES=(
    # gzip tests
    ["gzip,/tmp/test_output"]="/tmp/test_output.gz"
    ["gzip,/tmp/test_output.txt"]="/tmp/test_output.txt.gz"
    ["gzip,/tmp/test_output.zip"]="/tmp/test_output.gz"  # Should replace .zip with .gz
    ["gzip,/tmp/test_output.gz"]="/tmp/test_output.gz"   # Should keep existing .gz
    
    # xz tests
    ["xz,/tmp/test_output"]="/tmp/test_output.xz"
    ["xz,/tmp/test_output.txt"]="/tmp/test_output.txt.xz"
    ["xz,/tmp/test_output.gz"]="/tmp/test_output.xz"     # Should replace .gz with .xz
    ["xz,/tmp/test_output.xz"]="/tmp/test_output.xz"     # Should keep existing .xz
    
    # zip tests
    ["zip,/tmp/test_output"]="/tmp/test_output.zip"
    ["zip,/tmp/test_output.txt"]="/tmp/test_output.txt.zip"
    ["zip,/tmp/test_output.gz"]="/tmp/test_output.zip"   # Should replace .gz with .zip
    ["zip,/tmp/test_output.zip"]="/tmp/test_output.zip"  # Should keep existing .zip
    
    # tar.gz tests
    ["tar.gz,/tmp/test_output"]="/tmp/test_output.tar.gz"
    ["tar.gz,/tmp/test_output.txt"]="/tmp/test_output.txt.tar.gz"
    ["tar.gz,/tmp/test_output.zip"]="/tmp/test_output.tar.gz"     # Should replace .zip with .tar.gz
    ["tar.gz,/tmp/test_output.tar.gz"]="/tmp/test_output.tar.gz"  # Should keep existing .tar.gz
    
    # tar.xz tests
    ["tar.xz,/tmp/test_output"]="/tmp/test_output.tar.xz"
    ["tar.xz,/tmp/test_output.txt"]="/tmp/test_output.txt.tar.xz"
    ["tar.xz,/tmp/test_output.gz"]="/tmp/test_output.tar.xz"      # Should replace .gz with .tar.xz
    ["tar.xz,/tmp/test_output.tar.xz"]="/tmp/test_output.tar.xz"  # Should keep existing .tar.xz
)

PASSED=0
FAILED=0

for test_case in "${!TEST_CASES[@]}"; do
    IFS=',' read -r compression_type output_path <<< "$test_case"
    expected_file="${TEST_CASES[$test_case]}"
    
    echo "Testing: $compression_type compression with output '$output_path'"
    echo "Expected: $expected_file"
    
    # Clean up any existing files
    rm -f "$output_path"* "$expected_file"
    
    # Run compression
    if ./bella -input "$TEST_FILE" -output "$output_path" -compression compress -compresstype "$compression_type" -progress=false >/dev/null 2>&1; then
        if [ -f "$expected_file" ]; then
            echo "✓ PASS: File created with correct extension"
            PASSED=$((PASSED + 1))
        else
            echo "✗ FAIL: Expected file not found"
            echo "  Files created:"
            ls -la "$output_path"* 2>/dev/null || echo "  No files found"
            FAILED=$((FAILED + 1))
        fi
    else
        echo "✗ FAIL: Compression command failed"
        FAILED=$((FAILED + 1))
    fi
    
    # Clean up
    rm -f "$output_path"* "$expected_file"
    echo ""
done

# Test directory compression extension handling
echo "Testing directory compression extension handling..."

TEST_DIR="/tmp/extension_test_dir"
rm -rf "$TEST_DIR"
mkdir -p "$TEST_DIR"
echo "Dir file 1" > "$TEST_DIR/file1.txt"
echo "Dir file 2" > "$TEST_DIR/file2.txt"

# Test directory compression with different extensions
declare -A DIR_TEST_CASES=(
    ["tar.gz,/tmp/test_dir_output"]="/tmp/test_dir_output.tar.gz"
    ["tar.xz,/tmp/test_dir_output"]="/tmp/test_dir_output.tar.xz"
    ["zip,/tmp/test_dir_output"]="/tmp/test_dir_output.zip"
    ["tar.gz,/tmp/test_dir_output.zip"]="/tmp/test_dir_output.tar.gz"  # Should replace .zip with .tar.gz
)

for test_case in "${!DIR_TEST_CASES[@]}"; do
    IFS=',' read -r compression_type output_path <<< "$test_case"
    expected_file="${DIR_TEST_CASES[$test_case]}"
    
    echo "Testing directory: $compression_type compression with output '$output_path'"
    echo "Expected: $expected_file"
    
    # Clean up any existing files
    rm -f "$output_path"* "$expected_file"
    
    # Run compression
    if ./bella -input "$TEST_DIR" -output "$output_path" -compression compress -compresstype "$compression_type" -progress=false >/dev/null 2>&1; then
        if [ -f "$expected_file" ]; then
            echo "✓ PASS: Directory archive created with correct extension"
            PASSED=$((PASSED + 1))
        else
            echo "✗ FAIL: Expected directory archive not found"
            echo "  Files created:"
            ls -la "$output_path"* 2>/dev/null || echo "  No files found"
            FAILED=$((FAILED + 1))
        fi
    else
        echo "✗ FAIL: Directory compression command failed"
        FAILED=$((FAILED + 1))
    fi
    
    # Clean up
    rm -f "$output_path"* "$expected_file"
    echo ""
done

# Summary
echo "=== Test Summary ==="
echo "Passed: $PASSED"
echo "Failed: $FAILED"
echo "Total:  $((PASSED + FAILED))"

if [ $FAILED -eq 0 ]; then
    echo "✓ All tests passed!"
else
    echo "✗ Some tests failed!"
fi

# Cleanup
rm -f "$TEST_FILE"
rm -rf "$TEST_DIR"

echo "Extension testing completed."
