#!/bin/bash

# Test script to verify tar.gz and tar.xz single-file compression fixes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}Testing tar.gz and tar.xz single-file compression fixes...${NC}"

# Create test file
TEST_FILE="/tmp/test_single_file.txt"
echo "This is a test file for single-file tar compression." > "$TEST_FILE"
echo "It should be properly archived in a tar format." >> "$TEST_FILE"
echo "Line 3 with additional content." >> "$TEST_FILE"

# Test tar.gz compression
echo -e "${YELLOW}Testing tar.gz single-file compression...${NC}"
if ./bella -input "$TEST_FILE" -output "/tmp/test_single.tar.gz" -compression compress -compresstype tar.gz; then
    echo -e "${GREEN}✓ tar.gz compression succeeded${NC}"
    
    # Verify it's a proper tar.gz file
    if file "/tmp/test_single.tar.gz" | grep -q "gzip compressed"; then
        echo -e "${GREEN}✓ Output is properly gzip compressed${NC}"
        
        # Try to extract with standard tar command
        if tar -tzf "/tmp/test_single.tar.gz" | grep -q "test_single_file.txt"; then
            echo -e "${GREEN}✓ tar.gz contains proper tar archive${NC}"
        else
            echo -e "${RED}✗ tar.gz does not contain proper tar archive${NC}"
        fi
    else
        echo -e "${RED}✗ Output is not properly gzip compressed${NC}"
    fi
else
    echo -e "${RED}✗ tar.gz compression failed${NC}"
fi

# Test tar.xz compression
echo -e "${YELLOW}Testing tar.xz single-file compression...${NC}"
if ./bella -input "$TEST_FILE" -output "/tmp/test_single.tar.xz" -compression compress -compresstype tar.xz; then
    echo -e "${GREEN}✓ tar.xz compression succeeded${NC}"
    
    # Verify it's a proper tar.xz file
    if file "/tmp/test_single.tar.xz" | grep -q "XZ compressed"; then
        echo -e "${GREEN}✓ Output is properly xz compressed${NC}"
        
        # Try to extract with standard tar command
        if tar -tJf "/tmp/test_single.tar.xz" | grep -q "test_single_file.txt"; then
            echo -e "${GREEN}✓ tar.xz contains proper tar archive${NC}"
        else
            echo -e "${RED}✗ tar.xz does not contain proper tar archive${NC}"
        fi
    else
        echo -e "${RED}✗ Output is not properly xz compressed${NC}"
    fi
else
    echo -e "${RED}✗ tar.xz compression failed${NC}"
fi

# Test decompression
echo -e "${YELLOW}Testing decompression...${NC}"
if ./bella -input "/tmp/test_single.tar.gz" -output "/tmp/extracted_from_tar_gz.txt" -compression decompress; then
    if cmp -s "$TEST_FILE" "/tmp/extracted_from_tar_gz.txt"; then
        echo -e "${GREEN}✓ tar.gz decompression successful and matches original${NC}"
    else
        echo -e "${RED}✗ tar.gz decompressed file doesn't match original${NC}"
    fi
else
    echo -e "${RED}✗ tar.gz decompression failed${NC}"
fi

if ./bella -input "/tmp/test_single.tar.xz" -output "/tmp/extracted_from_tar_xz.txt" -compression decompress; then
    if cmp -s "$TEST_FILE" "/tmp/extracted_from_tar_xz.txt"; then
        echo -e "${GREEN}✓ tar.xz decompression successful and matches original${NC}"
    else
        echo -e "${RED}✗ tar.xz decompressed file doesn't match original${NC}"
    fi
else
    echo -e "${RED}✗ tar.xz decompression failed${NC}"
fi

# Cleanup
rm -f "$TEST_FILE" "/tmp/test_single.tar.gz" "/tmp/test_single.tar.xz" "/tmp/extracted_from_tar_gz.txt" "/tmp/extracted_from_tar_xz.txt"

echo -e "${BLUE}Test completed.${NC}"
