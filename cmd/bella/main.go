package main

import (
	"flag"
	"fmt"
	"os"

	"bella/pkg/copier"
	"bella/pkg/logging"
	"bella/pkg/ui"
)

func showHelp() {
	fmt.Println("Bella - Advanced Data Copier")
	fmt.Println()
	fmt.Println("USAGE:")
	fmt.Println("  bella                    Launch interactive UI")
	fmt.Println("  bella [options]          Run in command-line mode")
	fmt.Println("  bella -h, --help         Show this help")
	fmt.Println()
	fmt.Println("COPY OPTIONS:")
	fmt.Println("  -input string            Input file, directory, or device")
	fmt.Println("  -output string           Output file, directory, or device")
	fmt.Println("  -bs int                  Block size in bytes (default 4194304)")
	fmt.Println("  -compression string      Compression mode: none|compress|decompress|auto (default \"none\")")
	fmt.Println("  -compresstype string     Compression type: tar.gz|tar.xz|zip (default \"tar.gz\")")
	fmt.Println("  -count int               Number of blocks to copy, -1 for all (default -1)")
	fmt.Println("  -skip int                Blocks to skip at start of input")
	fmt.Println("  -seek int                Blocks to seek at start of output")
	fmt.Println("  -sparse                  Enable sparse file copy")
	fmt.Println("  -skip-bad                Skip read errors and fill with zeros")
	fmt.Println("  -verify                  Verify copy by comparing source and destination *after* copy")
	fmt.Println("  -append                  Append to output file instead of overwriting")
	fmt.Println("  -checksum string         Calculate checksum using algorithm: sha256|md5|sha1")
	fmt.Println("  -no-offload              Disable kernel copy offload (enabled by default)")
	fmt.Println()
	fmt.Println("WIPE OPTIONS:")
	fmt.Println("  -wipe string             Wipe mode: zero|random")
	fmt.Println("  -passes int              Number of wipe passes (default 1)")
	fmt.Println()
	fmt.Println("VERIFY OPTIONS:")
	fmt.Println("  -verify-only             Verify source and destination are identical (no copy)")
	fmt.Println()
	fmt.Println("GENERAL OPTIONS:")
	fmt.Println("  -progress                Show progress bar (default true)")
	fmt.Println("  -dry-run                 Show what would be done without executing")
	fmt.Println()
	fmt.Println("EXAMPLES:")
	fmt.Println("  bella -input file.txt -output copy.txt")
	fmt.Println("  bella -input my_folder -output archive -compression compress -compresstype tar.xz")
	fmt.Println("  bella -input file.gz -output file.txt -compression decompress")
	fmt.Println("  bella -input source.img -output copy.img -verify")
	fmt.Println("  bella -input source.img -output copy.img -verify-only")
	fmt.Println("  bella -input data.bin -output backup.bin -checksum sha256")
	fmt.Println("  bella -input new.txt -output existing.txt -append")
	fmt.Println("  bella -wipe random -output /dev/sdX")
}

func main() {

	logging.Init()

	for _, arg := range os.Args {
		if arg == "-h" || arg == "--help" {
			showHelp()
			return
		}
	}

	if len(os.Args) < 2 {
		app := ui.NewApp()
		if err := app.Run(); err != nil {
			fmt.Fprintf(os.Stderr, "UI Error: %v\n", err)
			os.Exit(1)
		}
		return
	}

	cfg := copier.NewConfig()
	var noOffload bool
	var verifyOnly bool

	fs := flag.NewFlagSet("bella", flag.ExitOnError)
	fs.Usage = showHelp

	fs.StringVar(&cfg.Input, "input", "", "Input file, directory, or device")
	fs.StringVar(&cfg.Output, "output", "", "Output file, directory, or device")
	fs.IntVar(&cfg.BlockSize, "bs", 4*1024*1024, "Block size in bytes")
	fs.BoolVar(&cfg.Progress, "progress", true, "Show progress bar")
	fs.IntVar(&cfg.Count, "count", -1, "Number of blocks to copy (-1 for all)")
	fs.Int64Var(&cfg.Skip, "skip", 0, "Blocks to skip at start of input")
	fs.Int64Var(&cfg.Seek, "seek", 0, "Blocks to seek at start of output")
	fs.StringVar(&cfg.Compression, "compression", "none", "Compression mode: compress|decompress|auto|none")
	fs.StringVar(&cfg.CompressionType, "compresstype", "tar.gz", "Compression type: tar.gz|tar.xz|zip")
	fs.BoolVar(&cfg.Sparse, "sparse", false, "Enable sparse file copy")
	fs.BoolVar(&cfg.SkipBadSectors, "skip-bad", false, "Skip read errors and fill with zeros")
	fs.BoolVar(&cfg.Verify, "verify", false, "Verify copy by comparing source and destination *after* copy")
	fs.BoolVar(&cfg.Append, "append", false, "Append to output file instead of overwriting")
	fs.BoolVar(&noOffload, "no-offload", false, "Disable kernel copy offload (enabled by default)")
	fs.BoolVar(&cfg.DryRun, "dry-run", false, "Show what would be done without executing")
	fs.StringVar(&cfg.WipeMode, "wipe", "", "Wipe mode: 'zero'|'random'")
	fs.IntVar(&cfg.WipePasses, "passes", 1, "Number of passes for wipe operation")
	fs.StringVar(&cfg.Checksum, "checksum", "", "Calculate checksum using algorithm: sha256|md5|sha1")
	fs.BoolVar(&verifyOnly, "verify-only", false, "Verify source and destination are identical (no copy)")

	fs.Parse(os.Args[1:])

	if noOffload {
		cfg.UseCopyOffload = false
	}

	hasInput := cfg.Input != ""
	hasOutput := cfg.Output != ""
	isWipe := cfg.WipeMode != ""

	if verifyOnly {
		if !hasInput || !hasOutput {
			fmt.Fprintln(os.Stderr, "Error: -verify-only requires both -input and -output to be specified.")
			os.Exit(1)
		}
		cfg.Operation = copier.OpVerify
	} else if isWipe {
		cfg.Operation = copier.OpWipe
	} else if hasInput && hasOutput {
		cfg.Operation = copier.OpCopy
	} else {
		fmt.Fprintln(os.Stderr, "Error: Invalid or incomplete flags specified.")
		fmt.Fprintln(os.Stderr, "Please specify an operation (-input/-output for copy, -wipe, or -verify-only).")
		fmt.Fprintln(os.Stderr, "Use -h or --help for more information.")
		os.Exit(1)
	}

	if err := cfg.Validate(); err != nil {
		fmt.Fprintf(os.Stderr, "Configuration Error: %v\n", err)
		os.Exit(1)
	}

	if err := ui.RunCLIProgress(cfg); err != nil {
		fmt.Fprintf(os.Stderr, "\nExecution Error: %v\n", err)
		os.Exit(1)
	}
}
