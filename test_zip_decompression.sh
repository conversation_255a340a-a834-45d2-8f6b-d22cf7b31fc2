#!/bin/bash

# Test ZIP decompression functionality

set -e

echo "Testing ZIP decompression functionality..."

# Create test directory structure
TEST_DIR="/tmp/zip_test_source"
rm -rf "$TEST_DIR"
mkdir -p "$TEST_DIR/subdir1" "$TEST_DIR/subdir2"

echo "Creating test files..."
echo "File 1 content for ZIP testing" > "$TEST_DIR/file1.txt"
echo "File 2 content for ZIP testing" > "$TEST_DIR/file2.txt"
echo "Subdirectory file 1" > "$TEST_DIR/subdir1/subfile1.txt"
echo "Subdirectory file 2" > "$TEST_DIR/subdir2/subfile2.txt"

echo "Test directory structure created:"
find "$TEST_DIR" -type f

# Create ZIP archive using bella
echo "Creating ZIP archive..."
./bella -input "$TEST_DIR" -output "/tmp/test_archive.zip" -compression compress -compresstype zip -progress=false >/dev/null 2>&1

if [ ! -f "/tmp/test_archive.zip" ]; then
    echo "✗ Failed to create ZIP archive"
    exit 1
fi

echo "✓ ZIP archive created successfully"
ls -la /tmp/test_archive.zip

# Test ZIP decompression
EXTRACT_DIR="/tmp/zip_extract_test"
rm -rf "$EXTRACT_DIR"
mkdir -p "$EXTRACT_DIR"

echo "Testing ZIP decompression..."
./bella -input "/tmp/test_archive.zip" -output "$EXTRACT_DIR" -compression decompress -progress=false >/dev/null 2>&1

echo "Checking extracted files..."

# Check if files were extracted correctly
if [ -f "$EXTRACT_DIR/file1.txt" ]; then
    echo "✓ file1.txt extracted"
    if grep -q "File 1 content for ZIP testing" "$EXTRACT_DIR/file1.txt"; then
        echo "✓ file1.txt content is correct"
    else
        echo "✗ file1.txt content is incorrect"
    fi
else
    echo "✗ file1.txt not extracted"
fi

if [ -f "$EXTRACT_DIR/file2.txt" ]; then
    echo "✓ file2.txt extracted"
    if grep -q "File 2 content for ZIP testing" "$EXTRACT_DIR/file2.txt"; then
        echo "✓ file2.txt content is correct"
    else
        echo "✗ file2.txt content is incorrect"
    fi
else
    echo "✗ file2.txt not extracted"
fi

if [ -f "$EXTRACT_DIR/subdir1/subfile1.txt" ]; then
    echo "✓ subdir1/subfile1.txt extracted"
    if grep -q "Subdirectory file 1" "$EXTRACT_DIR/subdir1/subfile1.txt"; then
        echo "✓ subdir1/subfile1.txt content is correct"
    else
        echo "✗ subdir1/subfile1.txt content is incorrect"
    fi
else
    echo "✗ subdir1/subfile1.txt not extracted"
fi

if [ -f "$EXTRACT_DIR/subdir2/subfile2.txt" ]; then
    echo "✓ subdir2/subfile2.txt extracted"
    if grep -q "Subdirectory file 2" "$EXTRACT_DIR/subdir2/subfile2.txt"; then
        echo "✓ subdir2/subfile2.txt content is correct"
    else
        echo "✗ subdir2/subfile2.txt content is incorrect"
    fi
else
    echo "✗ subdir2/subfile2.txt not extracted"
fi

# Test auto-decompression
echo "Testing auto-decompression..."
AUTO_EXTRACT_DIR="/tmp/zip_auto_extract_test"
rm -rf "$AUTO_EXTRACT_DIR"
mkdir -p "$AUTO_EXTRACT_DIR"

./bella -input "/tmp/test_archive.zip" -output "$AUTO_EXTRACT_DIR" -compression auto -progress=false >/dev/null 2>&1

if [ -f "$AUTO_EXTRACT_DIR/file1.txt" ]; then
    echo "✓ Auto-decompression works"
else
    echo "✗ Auto-decompression failed"
fi

# Test single file ZIP
echo "Testing single file ZIP compression and decompression..."
SINGLE_FILE="/tmp/single_test.txt"
echo "Single file content for ZIP testing" > "$SINGLE_FILE"

./bella -input "$SINGLE_FILE" -output "/tmp/single_test.zip" -compression compress -compresstype zip -progress=false >/dev/null 2>&1

if [ -f "/tmp/single_test.zip" ]; then
    echo "✓ Single file ZIP created"
    
    # Extract single file ZIP
    ./bella -input "/tmp/single_test.zip" -output "/tmp/single_extracted.txt" -compression decompress -progress=false >/dev/null 2>&1
    
    if [ -f "/tmp/single_extracted.txt" ]; then
        echo "✓ Single file ZIP extracted"
        if grep -q "Single file content for ZIP testing" "/tmp/single_extracted.txt"; then
            echo "✓ Single file ZIP content is correct"
        else
            echo "✗ Single file ZIP content is incorrect"
        fi
    else
        echo "✗ Single file ZIP extraction failed"
    fi
else
    echo "✗ Single file ZIP creation failed"
fi

echo "Extracted directory structure:"
find "$EXTRACT_DIR" -type f 2>/dev/null || echo "No files found in extract directory"

# Cleanup
rm -rf "$TEST_DIR" "$EXTRACT_DIR" "$AUTO_EXTRACT_DIR" "/tmp/test_archive.zip" "$SINGLE_FILE" "/tmp/single_test.zip" "/tmp/single_extracted.txt"

echo "ZIP decompression test completed."
