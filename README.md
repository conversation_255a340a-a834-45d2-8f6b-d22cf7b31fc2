# Bella - Advanced Data Copier

Bella is a powerful, cross-platform data copying tool that provides both
command-line and terminal UI interfaces for copying files, directories, and
devices with advanced features like compression, verification, and secure
wiping.

## Features

- **Multiple Interfaces**: Command-line and interactive terminal UI
- **Device Support**: Copy to/from block devices and regular files
- **Advanced Compression**: Support for gzip, xz, zip, tar.gz, and tar.xz formats
- **Standalone Compression/Decompression**: Dedicated UI forms for compression
  operations
- **Auto-Detection**: Automatic archive format detection from file extensions
- **Compression Levels**: Configurable compression levels for optimal size/speed
  balance
- **Verification**: File integrity verification with multiple checksum algorithms
- **Secure Wiping**: Multiple-pass secure data wiping
- **Performance**: Multi-threaded operations and kernel copy offload
- **Progress Tracking**: Real-time progress reporting with detailed statistics
- **File Conflict Handling**: Interactive prompts for overwrite/append/cancel
  decisions
- **Advanced Options**: Sparse files, bad sector handling, and more

## Installation

```bash
go build -o bella ./cmd/bella/
```
## Usage

### Interactive Mode (Default)

Simply run Bella without arguments to launch the interactive terminal UI:

```bash
./bella
```
### Command Line Mode

#### Copy Operations

```bash
# Basic copy
./bella -input /path/to/source -output /path/to/destination

# Copy with compression
./bella -input /path/to/source -output /path/to/destination.gz -compression compress -compresstype gzip

# Copy with decompression
./bella -input /path/to/source.gz -output /path/to/destination -compression decompress -compresstype gzip

# Auto-detect compression (decompress .gz files automatically)
./bella -input /path/to/source.gz -output /path/to/destination -compression auto

# Copy with verification
./bella -input /path/to/source -output /path/to/destination -verify

# Advanced copy with custom block size and sparse file support
./bella -input /path/to/source -output /path/to/destination -bs 8388608 -sparse
```
#### Verify Operations

```bash
# Verify two files are identical
./bella -input /path/to/source -output /path/to/destination -verify-only
```
#### Wipe Operations

```bash
# Secure wipe with random data (default)
./bella -wipe random -output /dev/sdX

# Wipe with zeros
./bella -wipe zero -output /dev/sdX

# Multiple-pass wipe
./bella -wipe random -passes 3 -output /dev/sdX
```
## Compression System

Bella uses a unified compression system that prevents conflicts between
compression and decompression operations:

### Compression Modes

- `none`: No compression or decompression (default)
- `compress`: Compress data during copy operation
- `decompress`: Decompress data during copy operation
- `auto`: Automatically decompress files with `.gz` extension

### Compression Types

- `gzip`: Standard gzip compression for single files
- `xz`: High-efficiency xz compression for single files
- `zip`: ZIP archive format for files and directories
- `tar.gz`: Gzip-compressed tar archives for directories
- `tar.xz`: XZ-compressed tar archives for directories

### Compression Levels

- `Good`: Fast compression with reasonable size reduction
- `Better`: Balanced compression speed and ratio
- `Best`: Maximum compression with slower speed
- `N/A`: For ZIP format (compression level handled internally)

### Examples

```bash
# Compress a file with gzip
./bella -input largefile.txt -output largefile.txt.gz -compression compress -compresstype gzip

# Compress a directory as tar.gz
./bella -input /path/to/directory -output archive.tar.gz -compression compress -compresstype tar.gz

# Compress with maximum compression level
./bella -input largefile.txt -output largefile.txt.xz -compression compress -compresstype xz -compressionlevel Best

# Decompress various formats
./bella -input archive.gz -output extracted.txt -compression decompress -compresstype gzip
./bella -input archive.tar.xz -output /extract/path -compression decompress -compresstype tar.xz
./bella -input archive.zip -output /extract/path -compression decompress -compresstype zip

# Auto-decompress (detects format from extension)
./bella -input archive.tar.gz -output /extract/path -compression auto
./bella -input file.xz -output file.txt -compression auto
```
## Command Line Options

### General Options

- `\\\\\\\\-input`: Source file, directory, or device
- `\\\\\\\\-output`: Destination file, directory, or device
- `\\\\\\\\-bs`: Block size in bytes (default: 4MB, use "auto" for automatic)
- `\\\\\\\\-progress`: Show progress bar (default: true)
- `\\\\\\\\-dry-run`: Show what would be done without executing

### Copy Options

- `\\\\\\\\-compression`: Compression mode (none|compress|decompress|auto)
- `\\\\\\\\-compresstype`: Compression algorithm (gzip|xz|zip|tar.gz|tar.xz)
- `\\\\\\\\-compressionlevel`: Compression level (Good|Better|Best)
- `\\\\\\\\-count`: Number of blocks to copy (-1 for all)
- `\\\\\\\\-skip`: Blocks to skip at start of input
- `\\\\\\\\-seek`: Blocks to seek at start of output
- `\\\\\\\\-sparse`: Enable sparse file copy
- `\\\\\\\\-skip-bad`: Skip read errors and fill with zeros
- `\\\\\\\\-verify`: Verify copy by comparing source and destination
- `\\\\\\\\-p`: Preserve attributes for directories
- `\\\\\\\\-append`: Append to output file instead of overwriting
- `\\\\\\\\-checksum`: Calculate checksum using algorithm (sha256|md5|sha1)
- `\\\\\\\\-no-offload`: Disable kernel copy offload (enabled by default)

### Wipe Options

- `\\\\\\\\-wipe`: Wipe mode (zero|random)
- `\\\\\\\\-passes`: Number of wipe passes (default: 1)

## Interactive UI Features

The terminal UI provides an intuitive interface with:

- **Main Menu**: Copy, Compress/Archive, Decompress/Extract, Verify, Wipe, List
  Devices, Quit
- **Standalone Compression Forms**: Dedicated interfaces for compression and
  decompression operations
- **Device Browser**: Browse and select storage devices
- **Form-based Input**: Easy configuration of all options
- **Real-time Progress**: Modal progress dialogs with clean backgrounds
- **Comprehensive Options**: All command-line features available in UI
- **Compression Type Selection**: Dropdown menus for format and level selection
- **Dynamic UI Updates**: Compression level options update based on selected
  format
- **Checksum Selection**: Dropdown menu for choosing checksum algorithms
- **File Conflict Dialogs**: Interactive prompts for handling existing files

### UI Navigation

- Use arrow keys to navigate
- Press Enter to select
- Press Esc to go back
- All forms include browse buttons for easy device/file selection

## Performance Features

- **Multi-threading**: Configurable thread count for improved performance
- **Kernel Copy Offload**: Enabled by default for maximum speed. Uses `
  copy_file_range()` when available.
- **Multi-Stage Operations**: When combining kernel offload with verification
  and/or compression, Bella automatically uses a multi-stage process to ensure
  both speed and correctness.
- **Optimized Block Sizes**: Automatic block size detection for optimal
  performance
- **Sparse File Support**: Efficient handling of sparse files

## Checksum Support

Bella supports calculating checksums during copy operations to verify data
integrity:

- **SHA256**: Cryptographically secure hash (recommended)
- **MD5**: Fast hash for basic integrity checking
- **SHA1**: Balanced security and performance

Checksums are calculated on-the-fly during copying and displayed upon
completion.

## File Conflict Handling

When copying to an existing file, Bella provides several options:

- **Interactive Prompt**: Choose to overwrite, append, verify only, or cancel
- **Append Mode**: Use `\\-append` flag to append data to existing files
- **Verify Only**: Compare files without copying when conflicts are detected
- **Consistent Behavior**: Same conflict handling in both CLI and GUI modes

## Safety Features

- **Dry Run Mode**: Test operations without making changes
- **Verification**: Built-in file integrity checking
- **Bad Sector Handling**: Skip corrupted sectors and continue
- **Conflict Detection**: Prevents incompatible option combinations
- **File Conflict Resolution**: Interactive prompts prevent accidental overwrites

## Examples

### Device Cloning with Verification

```bash
sudo ./bella -input /dev/sda -output /dev/sdb -verify -progress
```
### Compressed Backup

```bash
# Create a compressed tar.gz backup of a directory
./bella -input /home/<USER>/documents -output /backup/documents.tar.gz -compression compress -compresstype tar.gz

# Create a high-compression tar.xz backup
./bella -input /home/<USER>/documents -output /backup/documents.tar.xz -compression compress -compresstype tar.xz -compressionlevel Best
```
### Secure Device Wipe

```bash
sudo ./bella -wipe random -passes 3 -output /dev/sdX
```
### Copy with Checksum Verification

```bash
# Copy file and calculate SHA256 checksum
./bella -input source.txt -output destination.txt -checksum sha256

# Copy with MD5 checksum
./bella -input largefile.bin -output backup.bin -checksum md5

# Copy with verification and checksum
./bella -input important.dat -output backup.dat -verify -checksum sha1
```
### File Conflict Handling

```bash
# Append to existing file instead of overwriting
./bella -input new_data.txt -output existing_file.txt -append

# Interactive prompt when file exists (without -append flag)
./bella -input source.txt -output existing.txt
# Prompts: (o)verwrite, (a)ppend, (v)erify only, (c)ancel
```
## Requirements

- Go 1.19 or later
- Linux, macOS, or Windows
- Root/Administrator privileges for device access

## License

\[Add your license information here]

