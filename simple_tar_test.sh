#!/bin/bash

# Simple test to verify tar.gz and tar.xz single-file compression

echo "Creating test file..."
echo "This is a test file for tar compression" > /tmp/simple_test.txt

echo "Testing tar.gz compression..."
./bella -input /tmp/simple_test.txt -output /tmp/simple_test.tar.gz -compression compress -compresstype tar.gz -progress=false

if [ -f "/tmp/simple_test.tar.gz" ]; then
    echo "✓ tar.gz file created"
    
    # Check if it's a proper tar.gz file
    if file /tmp/simple_test.tar.gz | grep -q "gzip compressed"; then
        echo "✓ File is gzip compressed"
        
        # Check if it contains a tar archive
        if tar -tzf /tmp/simple_test.tar.gz 2>/dev/null | grep -q "simple_test.txt"; then
            echo "✓ Contains proper tar archive with original filename"
        else
            echo "✗ Does not contain proper tar archive"
        fi
    else
        echo "✗ File is not gzip compressed"
    fi
else
    echo "✗ tar.gz file not created"
fi

echo "Testing tar.xz compression..."
./bella -input /tmp/simple_test.txt -output /tmp/simple_test.tar.xz -compression compress -compresstype tar.xz -progress=false

if [ -f "/tmp/simple_test.tar.xz" ]; then
    echo "✓ tar.xz file created"
    
    # Check if it's a proper tar.xz file
    if file /tmp/simple_test.tar.xz | grep -q "XZ compressed"; then
        echo "✓ File is xz compressed"
        
        # Check if it contains a tar archive
        if tar -tJf /tmp/simple_test.tar.xz 2>/dev/null | grep -q "simple_test.txt"; then
            echo "✓ Contains proper tar archive with original filename"
        else
            echo "✗ Does not contain proper tar archive"
        fi
    else
        echo "✗ File is not xz compressed"
    fi
else
    echo "✗ tar.xz file not created"
fi

# Cleanup
rm -f /tmp/simple_test.txt /tmp/simple_test.tar.gz /tmp/simple_test.tar.xz

echo "Test completed."
